# 告警数据源详情接口说明

## 接口概述

新增了一个查看告警数据源详情的接口，用于获取告警关联的原始数据详情。告警系统中每个告警都有对应的数据源，通过 `sourceType` 和 `sourceId` 字段关联。

## 接口信息

**接口地址**: `GET /alarm/info/source/{alarmId}`

**接口描述**: 根据告警ID获取对应数据源的详细信息

**权限要求**: `alarm:info:query`

## 数据源类型

根据字典表 `alarm_source_type`，系统支持以下数据源类型：

| 源类型值 | 源类型名称 | 实现状态 | 负责模块 |
|----------|------------|----------|----------|
| 1 | 隐患 | TODO | 隐患模块 |
| 2 | 应急事件 | TODO | 应急事件模块 |
| 3 | 应急预案 | TODO | 应急预案模块 |
| 4 | 应急物资 | TODO | 应急物资模块 |
| 5 | 应急通讯录 | TODO | 应急通讯录模块 |
| 6 | 气象预警 | TODO | 气象预警模块 |
| 7 | 应急救援队伍 | TODO | 应急救援队伍模块 |

## 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| alarmId | string | 是 | 告警ID |

## 响应格式

### 成功响应
```json
{
    "msg": "操作成功",
    "code": 200,
    "data": {
        "sourceType": "6",
        "sourceId": "warning123",
        "sourceTypeName": "气象预警",
        "sourceDetail": {
            // 具体的数据源详情，根据不同类型返回不同结构
        }
    }
}
```

### 错误响应
```json
{
    "msg": "告警信息不存在",
    "code": 500
}
```

## 各数据源详情结构

### 1. 气象预警 (sourceType = "6")
```json
{
    "sourceType": "6",
    "sourceId": "warning123",
    "sourceTypeName": "气象预警",
    "sourceDetail": {
        "warningId": "warning123",
        "message": "气象预警详情接口待实现",
        "todo": "请气象预警模块负责人实现此方法"
    }
}
```

### 2. 应急事件 (sourceType = "2")
```json
{
    "sourceType": "2",
    "sourceId": "event456",
    "sourceTypeName": "应急事件",
    "sourceDetail": {
        "eventId": "event456",
        "message": "应急事件详情接口待实现",
        "todo": "请应急事件模块负责人实现此方法"
    }
}
```

### 3. 其他类型 (TODO状态)
```json
{
    "sourceType": "1",
    "sourceId": "pitfall789",
    "sourceTypeName": "隐患",
    "sourceDetail": {
        "message": "隐患详情接口待实现",
        "status": "TODO"
    }
}
```

## 实现架构

### 当前实现
- **基础框架**：已完成告警数据源详情查询的基础架构
- **路由分发**：根据 `sourceType` 自动路由到对应的详情获取方法
- **TODO处理**：对未实现的数据源类型返回友好的TODO提示

### 待各模块实现
各模块负责人需要在 `AlarmServiceImpl` 中实现对应的详情获取方法：

#### 气象预警模块
```java
private Map<String, Object> getWeatherWarningDetail(String warningId) {
    // 调用气象预警Service的详情查询方法
    // 返回完整的预警详情数据
}
```

#### 应急事件模块
```java
private Map<String, Object> getEmergencyEventDetail(String eventId) {
    // 调用应急事件Service的详情查询方法
    // 返回完整的事件详情数据
}
```

#### 其他模块
类似地实现各自的详情获取方法。

## 使用示例

### JavaScript (Axios)
```javascript
// 获取告警数据源详情
const getAlarmSourceDetail = async (alarmId) => {
    try {
        const response = await axios.get(`/alarm/info/source/${alarmId}`);
        const data = response.data.data;
        
        console.log('数据源类型:', data.sourceTypeName);
        console.log('数据源ID:', data.sourceId);
        console.log('详情数据:', data.sourceDetail);
        
        return data;
    } catch (error) {
        console.error('获取数据源详情失败:', error);
    }
};

// 使用示例
getAlarmSourceDetail('alarm123').then(data => {
    if (data.sourceType === '6') {
        // 处理气象预警详情
        handleWeatherWarningDetail(data.sourceDetail);
    } else if (data.sourceType === '2') {
        // 处理应急事件详情
        handleEmergencyEventDetail(data.sourceDetail);
    }
});
```

### 前端展示建议
```javascript
// 根据数据源类型显示不同的详情页面
const showSourceDetail = (sourceData) => {
    switch (sourceData.sourceType) {
        case '6': // 气象预警
            showWeatherWarningDetail(sourceData.sourceDetail);
            break;
        case '2': // 应急事件
            showEmergencyEventDetail(sourceData.sourceDetail);
            break;
        default:
            showTodoMessage(sourceData.sourceDetail);
            break;
    }
};
```

## 扩展指南

### 为新数据源类型添加支持

1. **在 `AlarmServiceImpl.getAlarmSourceDetail()` 中添加新的 case**：
```java
case "8": // 新的数据源类型
    Map<String, Object> newDetail = getNewSourceDetail(sourceId);
    result.put("sourceDetail", newDetail);
    result.put("sourceTypeName", "新数据源");
    break;
```

2. **实现对应的详情获取方法**：
```java
private Map<String, Object> getNewSourceDetail(String sourceId) {
    // 调用对应模块的Service
    // 返回详情数据
}
```

3. **注入对应模块的Service**：
```java
@Autowired
private INewSourceService newSourceService;
```

## 注意事项

1. **权限控制**：接口使用 `alarm:info:query` 权限，确保用户有查看告警详情的权限
2. **数据一致性**：各模块实现详情接口时，应确保返回的数据与告警创建时的源数据一致
3. **错误处理**：当源数据不存在时，应返回友好的错误信息
4. **性能考虑**：详情查询可能涉及跨模块调用，注意性能优化
5. **版本兼容**：新增字段时要考虑向后兼容性

## 开发计划

### 第一阶段（当前）
- ✅ 完成基础架构
- ✅ 实现路由分发逻辑
- ✅ 添加Controller接口

### 第二阶段（待各模块实现）
- ⏳ 气象预警模块实现详情接口
- ⏳ 应急事件模块实现详情接口
- ⏳ 其他模块逐步实现

### 第三阶段（优化）
- ⏳ 添加缓存机制
- ⏳ 性能优化
- ⏳ 增加更多数据源类型支持

---

**接口版本**: v1.0.0  
**最后更新**: 2025-06-18  
**维护人员**: 告警模块开发团队
