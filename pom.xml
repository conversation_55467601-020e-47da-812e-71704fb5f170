<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	
    <groupId>com.tocc</groupId>
    <artifactId>tocc</artifactId>
    <version>3.8.9</version>

    <name>tocc</name>
    <description>广西公路水路安全畅通与应急处置系统</description>
    
    <properties>
        <tocc.version>3.8.9</tocc.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <maven-jar-plugin.version>3.1.1</maven-jar-plugin.version>
        <spring-boot.version>2.5.15</spring-boot.version>
        <druid.version>1.2.23</druid.version>
        <bitwalker.version>1.21</bitwalker.version>
        <swagger.version>3.0.0</swagger.version>
        <kaptcha.version>2.3.3</kaptcha.version>
        <pagehelper.boot.version>1.4.7</pagehelper.boot.version>
        <fastjson.version>2.0.57</fastjson.version>
        <oshi.version>6.8.1</oshi.version>
        <commons.io.version>2.19.0</commons.io.version>
        <poi.version>4.1.2</poi.version>
        <velocity.version>2.3</velocity.version>
        <jwt.version>0.9.1</jwt.version>
        <!-- Spring Cloud 版本 -->
        <spring-cloud.version>2020.0.6</spring-cloud.version>
        <!-- override dependency version -->
        <tomcat.version>9.0.105</tomcat.version>
        <logback.version>1.2.13</logback.version>
        <spring-security.version>5.7.12</spring-security.version>
        <spring-framework.version>5.3.39</spring-framework.version>
    </properties>

    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>

            <!-- 覆盖SpringFramework的依赖配置-->
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-framework-bom</artifactId>
                <version>${spring-framework.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- 覆盖SpringSecurity的依赖配置-->
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-bom</artifactId>
                <version>${spring-security.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringBoot的依赖配置-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- 覆盖logback的依赖配置-->
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-core</artifactId>
                <version>${logback.version}</version>
            </dependency>

            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>${logback.version}</version>
            </dependency>

            <!-- 覆盖tomcat的依赖配置-->
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-core</artifactId>
                <version>${tomcat.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-el</artifactId>
                <version>${tomcat.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-websocket</artifactId>
                <version>${tomcat.version}</version>
            </dependency>

            <!-- 阿里数据库连接池 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <!-- 解析客户端操作系统、浏览器等 -->
            <dependency>
                <groupId>eu.bitwalker</groupId>
                <artifactId>UserAgentUtils</artifactId>
                <version>${bitwalker.version}</version>
            </dependency>

            <!-- pagehelper 分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper.boot.version}</version>
            </dependency>

            <!-- 获取系统信息 -->
            <dependency>
                <groupId>com.github.oshi</groupId>
                <artifactId>oshi-core</artifactId>
                <version>${oshi.version}</version>
            </dependency>

            <!-- Swagger3依赖 -->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-boot-starter</artifactId>
                <version>${swagger.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.swagger</groupId>
                        <artifactId>swagger-models</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- io常用工具类 -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>

            <!-- excel工具 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <!-- velocity代码生成使用模板 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <!-- 阿里JSON解析器 -->
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!-- Token生成与解析-->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jwt.version}</version>
            </dependency>

            <!-- 验证码 -->
            <dependency>
                <groupId>pro.fessional</groupId>
                <artifactId>kaptcha</artifactId>
                <version>${kaptcha.version}</version>
            </dependency>

            <!-- 定时任务-->
            <dependency>
                <groupId>com.tocc</groupId>
                <artifactId>tocc-quartz</artifactId>
                <version>${tocc.version}</version>
            </dependency>

            <!-- 代码生成-->
            <dependency>
                <groupId>com.tocc</groupId>
                <artifactId>tocc-generator</artifactId>
                <version>${tocc.version}</version>
            </dependency>

            <!-- 核心模块-->
            <dependency>
                <groupId>com.tocc</groupId>
                <artifactId>tocc-framework</artifactId>
                <version>${tocc.version}</version>
            </dependency>

            <!-- 系统模块-->
            <dependency>
                <groupId>com.tocc</groupId>
                <artifactId>tocc-system</artifactId>
                <version>${tocc.version}</version>
            </dependency>

            <!-- 通用工具-->
            <dependency>
                <groupId>com.tocc</groupId>
                <artifactId>tocc-common</artifactId>
                <version>${tocc.version}</version>
            </dependency>

            <!-- 电话模块-->
            <dependency>
                <groupId>com.tocc</groupId>
                <artifactId>tocc-tel</artifactId>
                <version>${tocc.version}</version>
            </dependency>

            <!-- 应急模块-->
            <dependency>
                <groupId>com.tocc</groupId>
                <artifactId>tocc-emer</artifactId>
                <version>${tocc.version}</version>
            </dependency>

            <!-- 报警模块-->
            <dependency>
                <groupId>com.tocc</groupId>
                <artifactId>tocc-alarm</artifactId>
                <version>${tocc.version}</version>
            </dependency>

            <!-- 气象预警模块-->
            <dependency>
                <groupId>com.tocc</groupId>
                <artifactId>tocc-weather</artifactId>
                <version>${tocc.version}</version>
            </dependency>

            <!-- 视频会议模块-->
            <dependency>
                <groupId>com.tocc</groupId>
                <artifactId>tocc-live</artifactId>
                <version>${tocc.version}</version>
            </dependency>

            <!-- Feign客户端模块-->
            <dependency>
                <groupId>com.tocc</groupId>
                <artifactId>tocc-feign-clients</artifactId>
                <version>${tocc.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <modules>
        <module>tocc-admin</module>
        <module>tocc-framework</module>
        <module>tocc-system</module>
        <module>tocc-quartz</module>
        <module>tocc-generator</module>
        <module>tocc-common</module>
        <module>tocc-tel</module>
        <module>tocc-emer</module>
        <module>tocc-alarm</module>
        <module>tocc-weather</module>
        <module>tocc-live</module>
        <module>tocc-drill</module>
        <module>tocc-feign-clients</module>
    </modules>
    <packaging>pom</packaging>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
<!--                    <compilerArgs>-->
<!--                        <arg>-J&#45;&#45;add-opens=jdk.compiler/com.sun.tools.javac.processing=ALL-UNNAMED</arg>-->
<!--                        <arg>-J&#45;&#45;add-opens=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED</arg>-->
<!--                    </compilerArgs>-->
                </configuration>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

</project>