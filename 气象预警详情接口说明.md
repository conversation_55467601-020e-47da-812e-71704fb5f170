# 气象预警详情接口说明

## 概述

新增了两个相关的接口：
1. **气象预警详情查询接口**：根据预警ID查询预警详情
2. **告警数据源详情接口**：在告警管理中查看气象预警数据源详情

两个接口都基于 `detail-list` 接口的权限控制逻辑，确保数据一致性。

## 1. 气象预警详情查询接口

### 接口信息

**接口地址**: `GET /weather/warning/{warningId}`

**接口描述**: 根据预警ID查询预警详情，基于用户权限控制

**权限要求**: `weather:warning:query`

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| warningId | string | 是 | 预警ID |

### 权限控制逻辑

与 `detail-list` 接口完全一致：

1. **用户创建的预警**：立即可见
2. **用户被通知的预警**：被通知后可见
3. **同单位可见的预警**：单位内有人被通知后可见
4. **单位创建的预警**：单位及上层单位创建的预警可见

### 响应格式

#### 成功响应
```json
{
    "msg": "操作成功",
    "code": 200,
    "data": {
        "warningId": "86a2c865-1386-4c42-9869-3e365fe1e796",
        "warningType": "1",
        "warningTypeLabel": "暴雨预警",
        "warningLevel": "8",
        "warningLevelLabel": "红色预警",
        "warningContent": "测试列表权限",
        "preventionGuide": "测试列表权限",
        "affectedRoads": "测试列表权限",
        "issueTime": "2025-06-17 16:20:04",
        "expireTime": "2025-06-20 00:00:00",
        "status": "0",
        "statusLabel": "有效",
        "isNotified": "1",
        "isNotifiedLabel": "已通知",
        "createTime": "2025-06-17 16:20:29",
        "createBy": "admin",
        "affectedAreas": [
            {
                "warningId": "86a2c865-1386-4c42-9869-3e365fe1e796",
                "regionId": "450107",
                "regionName": "广西壮族自治区南宁市西乡塘区",
                "createTime": "2025-06-17 16:20:29"
            }
        ],
        "affectedAreasDesc": "广西壮族自治区南宁市青秀区、广西壮族自治区南宁市西乡塘区",
        "totalNotifications": 2,
        "confirmedNotifications": 0,
        "unconfirmedNotifications": 2,
        "timeoutNotifications": 0,
        "alarmType": "notified",
        "canConfirm": true,
        "canViewProgress": false,
        "currentUserConfirmStatus": "0",
        "currentUserConfirmTime": null,
        "notificationProgress": []
    }
}
```

#### 无权限或不存在
```json
{
    "msg": "预警信息不存在或无权限查看",
    "code": 500
}
```

### 返回字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| warningId | string | 预警ID |
| warningType | string | 预警类型 |
| warningTypeLabel | string | 预警类型标签 |
| warningLevel | string | 预警等级（5-8） |
| warningLevelLabel | string | 预警等级标签 |
| warningContent | string | 预警内容 |
| preventionGuide | string | 防范指南 |
| affectedRoads | string | 影响路段 |
| issueTime | string | 发布时间 |
| expireTime | string | 失效时间 |
| status | string | 状态（0=有效，1=失效，2=取消） |
| statusLabel | string | 状态标签 |
| isNotified | string | 是否已通知（0=未通知，1=已通知） |
| isNotifiedLabel | string | 通知状态标签 |
| affectedAreas | array | 影响区域列表 |
| affectedAreasDesc | string | 影响区域描述 |
| totalNotifications | int | 总通知数量 |
| confirmedNotifications | int | 已确认通知数量 |
| unconfirmedNotifications | int | 未确认通知数量 |
| timeoutNotifications | int | 超时通知数量 |
| alarmType | string | 用户角色（creator/org_creator/notified/org_viewer） |
| canConfirm | boolean | 是否可以确认 |
| canViewProgress | boolean | 是否可以查看通知进度 |
| currentUserConfirmStatus | string | 当前用户确认状态 |
| currentUserConfirmTime | string | 当前用户确认时间 |
| notificationProgress | array | 通知进度列表 |

## 2. 告警数据源详情接口（气象预警部分）

### 接口信息

**接口地址**: `GET /alarm/info/source/{alarmId}`

**接口描述**: 获取告警的数据源详情，当数据源类型为气象预警时，调用上述气象预警详情接口

**权限要求**: `alarm:info:query`

### 气象预警数据源响应格式

```json
{
    "msg": "操作成功",
    "code": 200,
    "data": {
        "sourceType": "6",
        "sourceId": "86a2c865-1386-4c42-9869-3e365fe1e796",
        "sourceTypeName": "气象预警",
        "sourceDetail": {
            "warningId": "86a2c865-1386-4c42-9869-3e365fe1e796",
            "warningType": "1",
            "warningTypeLabel": "暴雨预警",
            "warningLevel": "8",
            "warningLevelLabel": "红色预警",
            "warningContent": "测试列表权限",
            "preventionGuide": "测试列表权限",
            "affectedRoads": "测试列表权限",
            "issueTime": "2025-06-17 16:20:04",
            "expireTime": "2025-06-20 00:00:00",
            "status": "0",
            "statusLabel": "有效",
            "isNotified": "1",
            "isNotifiedLabel": "已通知",
            "affectedAreas": [...],
            "affectedAreasDesc": "广西壮族自治区南宁市青秀区、广西壮族自治区南宁市西乡塘区",
            "alarmType": "notified",
            "canConfirm": true,
            "canViewProgress": false,
            "totalNotifications": 2,
            "confirmedNotifications": 0,
            "unconfirmedNotifications": 2,
            "timeoutNotifications": 0
        }
    }
}
```

### 异常情况处理

#### 预警不存在或无权限
```json
{
    "sourceType": "6",
    "sourceId": "invalid_warning_id",
    "sourceTypeName": "气象预警",
    "sourceDetail": {
        "warningId": "invalid_warning_id",
        "message": "预警信息不存在或无权限查看",
        "status": "NOT_FOUND_OR_NO_PERMISSION"
    }
}
```

#### 服务不可用
```json
{
    "sourceType": "6",
    "sourceId": "warning_id",
    "sourceTypeName": "气象预警",
    "sourceDetail": {
        "warningId": "warning_id",
        "message": "气象预警服务未可用",
        "status": "SERVICE_UNAVAILABLE"
    }
}
```

## 使用示例

### JavaScript (Axios)

#### 查询气象预警详情
```javascript
// 查询气象预警详情
const getWeatherWarningDetail = async (warningId) => {
    try {
        const response = await axios.get(`/weather/warning/${warningId}`);
        const data = response.data.data;
        
        console.log('预警详情:', data);
        console.log('用户权限:', data.alarmType);
        console.log('可否确认:', data.canConfirm);
        
        return data;
    } catch (error) {
        if (error.response?.status === 500) {
            console.log('预警不存在或无权限查看');
        }
        console.error('获取预警详情失败:', error);
    }
};
```

#### 查询告警数据源详情
```javascript
// 查询告警数据源详情
const getAlarmSourceDetail = async (alarmId) => {
    try {
        const response = await axios.get(`/alarm/info/source/${alarmId}`);
        const data = response.data.data;
        
        if (data.sourceType === '6') {
            // 气象预警数据源
            console.log('气象预警详情:', data.sourceDetail);
            if (data.sourceDetail.status === 'NOT_FOUND_OR_NO_PERMISSION') {
                console.log('无权限查看该预警');
            }
        }
        
        return data;
    } catch (error) {
        console.error('获取告警数据源详情失败:', error);
    }
};
```

### 前端展示建议

```javascript
// 根据用户权限显示不同的操作按钮
const renderWarningActions = (warningData) => {
    const actions = [];
    
    if (warningData.canConfirm) {
        actions.push({
            text: '确认预警',
            type: 'primary',
            action: () => confirmWarning(warningData.warningId)
        });
    }
    
    if (warningData.canViewProgress) {
        actions.push({
            text: '查看通知进度',
            type: 'default',
            action: () => viewNotificationProgress(warningData.warningId)
        });
    }
    
    return actions;
};

// 显示预警等级颜色
const getWarningLevelColor = (level) => {
    const colors = {
        '5': '#1890ff', // 蓝色
        '6': '#faad14', // 黄色
        '7': '#fa8c16', // 橙色
        '8': '#f5222d'  // 红色
    };
    return colors[level] || '#d9d9d9';
};
```

## 注意事项

1. **权限一致性**：两个接口都基于相同的权限控制逻辑，确保数据一致性
2. **跨模块调用**：告警模块调用气象预警模块时使用了 `@Autowired(required = false)`，避免循环依赖
3. **异常处理**：完善的异常处理机制，确保服务稳定性
4. **数据完整性**：返回完整的预警详情数据，包括权限相关字段
5. **性能考虑**：避免重复查询，复用权限检查结果

## 开发建议

1. **缓存优化**：对于频繁查询的预警详情，可以考虑添加缓存
2. **日志记录**：重要操作都有详细的日志记录，便于问题排查
3. **权限测试**：建议使用不同权限的用户进行充分测试
4. **文档更新**：如有字段变更，及时更新接口文档

---

**接口版本**: v1.0.0  
**最后更新**: 2025-06-18  
**维护人员**: 气象预警模块开发团队
