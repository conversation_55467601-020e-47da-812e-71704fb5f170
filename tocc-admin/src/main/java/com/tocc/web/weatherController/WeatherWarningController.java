package com.tocc.web.weatherController;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import com.sun.javafx.collections.MappingChange;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.tocc.common.annotation.Log;
import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.common.enums.BusinessType;
import com.tocc.common.utils.poi.ExcelUtil;
import com.tocc.common.core.page.TableDataInfo;
import com.tocc.weather.domain.entity.WeatherWarning;
import com.tocc.weather.domain.dto.WeatherWarningDTO;
import com.tocc.weather.domain.dto.WeatherWarningCreateDTO;
import com.tocc.weather.domain.dto.WeatherWarningNotifySimpleDTO;
import com.tocc.weather.domain.vo.WeatherWarningVO;
import com.tocc.weather.service.IWeatherWarningService;
import com.tocc.weather.service.IWeatherWarningNotificationService;

/**
 * 气象预警信息Controller
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Api(tags = "气象预警管理")
@RestController
@RequestMapping("/weather/warning")
public class WeatherWarningController extends BaseController
{
    @Autowired
    private IWeatherWarningService weatherWarningService;

    /**
     * 查询气象预警详细列表（包含影响区域和通知记录）
     */
    @ApiOperation("查询气象预警详细列表")
    @PreAuthorize("@ss.hasPermi('weather:warning:list')")
    @GetMapping("/detail-list")
    public TableDataInfo getWarningDetailList(WeatherWarningDTO queryDTO)
    {
        startPage();
        List<WeatherWarningVO> list = weatherWarningService.selectWeatherWarningDetailList(queryDTO);
        return getDataTable(list);
    }

    /**
     * 导出气象预警信息列表
     */
    @ApiOperation("导出气象预警信息列表")
    @PreAuthorize("@ss.hasPermi('weather:warning:export')")
    @Log(title = "气象预警信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WeatherWarningDTO weatherWarningDTO)
    {
        List<WeatherWarning> list = weatherWarningService.selectWeatherWarningList(weatherWarningDTO);
        ExcelUtil<WeatherWarning> util = new ExcelUtil<WeatherWarning>(WeatherWarning.class);
        util.exportExcel(response, list, "气象预警信息数据");
    }

    /**
     * 获取气象预警信息详细信息
     */
    @ApiOperation("获取气象预警信息详细信息")
    @PreAuthorize("@ss.hasPermi('weather:warning:query')")
    @GetMapping(value = "/{warningId}")
    public AjaxResult getInfo(@PathVariable("warningId") String warningId)
    {
        WeatherWarningVO vo = weatherWarningService.selectWeatherWarningDetail(warningId);
        return success(vo);
    }

    /**
     * 新增气象预警信息
     */
    @ApiOperation("新增气象预警信息")
    @PreAuthorize("@ss.hasPermi('weather:warning:add')")
    @Log(title = "气象预警信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Valid @RequestBody WeatherWarningCreateDTO createDTO)
    {
        String warningId = weatherWarningService.createWarning(createDTO);
        return success("预警创建成功，ID：" + warningId);
    }

    /**
     * 发送预警通知
     */
    @ApiOperation("发送预警通知")
    @PreAuthorize("@ss.hasPermi('weather:warning:add')")
    @Log(title = "发送预警通知", businessType = BusinessType.OTHER)
    @PostMapping("/{warningId}/notify")
    public AjaxResult sendNotifications(@PathVariable String warningId,
                                      @RequestBody WeatherWarningNotifySimpleDTO notifyDTO)
    {
        int count = weatherWarningService.sendNotificationsByUserIds(warningId, notifyDTO.getUserIds());
        return success("通知发送成功，共" + count + "条");
    }

    /**
     * 修改气象预警信息
     */
    @ApiOperation("修改气象预警信息")
    @PreAuthorize("@ss.hasPermi('weather:warning:edit')")
    @Log(title = "气象预警信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WeatherWarning weatherWarning)
    {
        return toAjax(weatherWarningService.updateWeatherWarning(weatherWarning));
    }

    /**
     * 删除气象预警信息
     */
    @ApiOperation("删除气象预警信息")
    @PreAuthorize("@ss.hasPermi('weather:warning:remove')")
    @Log(title = "气象预警信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{warningIds}")
    public AjaxResult remove(@PathVariable String[] warningIds)
    {
        return toAjax(weatherWarningService.deleteWeatherWarningByWarningIds(warningIds));
    }

    /**
     * 更新预警状态
     */
    @ApiOperation("更新预警状态")
    @PreAuthorize("@ss.hasPermi('weather:warning:edit')")
    @Log(title = "更新预警状态", businessType = BusinessType.UPDATE)
    @PutMapping("/{warningId}/status")
    public AjaxResult updateStatus(@PathVariable String warningId, @RequestBody String status)
    {
        return toAjax(weatherWarningService.updateWeatherWarningStatus(warningId, status));
    }

    /**
     * 统计未失效的预警数量（按等级分组）
     */
    @ApiOperation("统计预警等级数量")
    @PreAuthorize("@ss.hasPermi('weather:warning:list')")
    @GetMapping("/statistics/level")
    public AjaxResult getWarningLevelStatistics() {
        Map<String, Integer> statistics = weatherWarningService.selectWarningLevelStatistics();

        // 构建返回结果，包含等级名称
        Map<String, Object> result = new HashMap<>();
        result.put("blueCount", statistics.get("5"));      // 蓝色预警数量
        result.put("yellowCount", statistics.get("6"));    // 黄色预警数量
        result.put("orangeCount", statistics.get("7"));    // 橙色预警数量
        result.put("redCount", statistics.get("8"));       // 红色预警数量

        // 计算总数
        int totalCount = statistics.values().stream().mapToInt(Integer::intValue).sum();
        result.put("totalCount", totalCount);

        // 添加详细信息
        Map<String, Object> details = new HashMap<>();
        details.put("5", Map.of("level", "5", "name", "蓝色预警", "count", statistics.get("5")));
        details.put("6", Map.of("level", "6", "name", "黄色预警", "count", statistics.get("6")));
        details.put("7", Map.of("level", "7", "name", "橙色预警", "count", statistics.get("7")));
        details.put("8", Map.of("level", "8", "name", "红色预警", "count", statistics.get("8")));
        result.put("details", details);

        return success(result);
    }

}
