# 预警等级统计接口说明

## 接口概述

基于 `detail-list` 接口的权限控制逻辑，新增了一个统计未失效预警数量的接口，按预警等级分组统计。

## 接口信息

**接口地址**: `GET /weather/warning/statistics/level`

**接口描述**: 统计未失效的预警数量（按等级分组），基于用户权限控制，只统计用户有权限查看的预警

**权限要求**: `weather:warning:list`

## 权限控制逻辑

该接口复用了 `detail-list` 接口的权限控制逻辑，确保用户只能统计到有权限查看的预警：

1. **用户创建的预警**：立即可见
2. **用户被通知的预警**：被通知后可见
3. **同单位可见的预警**：单位内有人被通知后可见
4. **单位创建的预警**：单位及上层单位创建的预警可见

## 统计规则

- **只统计有效状态的预警**：`status = '0'`（有效）
- **按预警等级分组**：
  - 蓝色预警：`warning_level = '5'`
  - 黄色预警：`warning_level = '6'`
  - 橙色预警：`warning_level = '7'`
  - 红色预警：`warning_level = '8'`

## 响应格式

```json
{
    "msg": "操作成功",
    "code": 200,
    "data": {
        "blueCount": 2,      // 蓝色预警数量
        "yellowCount": 5,    // 黄色预警数量
        "orangeCount": 3,    // 橙色预警数量
        "redCount": 1,       // 红色预警数量
        "totalCount": 11,    // 总预警数量
        "details": {
            "5": {
                "level": "5",
                "name": "蓝色预警",
                "count": 2
            },
            "6": {
                "level": "6",
                "name": "黄色预警",
                "count": 5
            },
            "7": {
                "level": "7",
                "name": "橙色预警",
                "count": 3
            },
            "8": {
                "level": "8",
                "name": "红色预警",
                "count": 1
            }
        }
    }
}
```

## 字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| blueCount | int | 蓝色预警数量 |
| yellowCount | int | 黄色预警数量 |
| orangeCount | int | 橙色预警数量 |
| redCount | int | 红色预警数量 |
| totalCount | int | 总预警数量 |
| details | object | 详细信息，包含每个等级的完整信息 |

## 使用示例

### JavaScript (Axios)
```javascript
// 获取预警等级统计
const getWarningStatistics = async () => {
    try {
        const response = await axios.get('/weather/warning/statistics/level');
        const data = response.data.data;
        
        console.log('总预警数量:', data.totalCount);
        console.log('红色预警:', data.redCount);
        console.log('橙色预警:', data.orangeCount);
        console.log('黄色预警:', data.yellowCount);
        console.log('蓝色预警:', data.blueCount);
        
        return data;
    } catch (error) {
        console.error('获取预警统计失败:', error);
    }
};
```

### 前端图表展示
```javascript
// 用于 ECharts 饼图
const chartData = [
    { name: '红色预警', value: data.redCount, itemStyle: { color: '#f5222d' } },
    { name: '橙色预警', value: data.orangeCount, itemStyle: { color: '#fa8c16' } },
    { name: '黄色预警', value: data.yellowCount, itemStyle: { color: '#faad14' } },
    { name: '蓝色预警', value: data.blueCount, itemStyle: { color: '#1890ff' } }
];

// 用于仪表盘显示
const dashboardData = {
    total: data.totalCount,
    critical: data.redCount + data.orangeCount, // 严重预警（红+橙）
    warning: data.yellowCount,                   // 一般预警（黄）
    info: data.blueCount                        // 提示预警（蓝）
};
```

## 实现细节

### Service 层实现
- 复用 `getVisibleWarningIds()` 方法获取用户可见的预警ID列表
- 遍历可见预警，过滤有效状态（`status = '0'`）
- 按 `warning_level` 字段分组统计

### 权限控制
- 与 `detail-list` 接口使用相同的权限控制逻辑
- 确保数据一致性：统计结果与列表查询结果基于相同的权限规则

### 性能考虑
- 当用户可见预警数量较大时，会逐个查询预警详情
- 建议后续优化：在 Mapper 层直接进行统计查询，减少内存占用

## 注意事项

1. **权限一致性**：统计结果与 `detail-list` 接口查询结果基于相同的权限控制
2. **状态过滤**：只统计有效状态（`status = '0'`）的预警
3. **实时性**：每次调用都会重新计算，确保数据实时性
4. **日志记录**：包含详细的日志记录，便于问题排查

## 测试建议

1. **权限测试**：使用不同权限的用户测试，确保统计结果正确
2. **数据一致性测试**：对比统计结果与 `detail-list` 接口的查询结果
3. **边界测试**：测试无预警、单一等级预警等边界情况
4. **性能测试**：在大量预警数据下测试接口响应时间

## 后续优化建议

1. **数据库层统计**：在 Mapper 层直接进行 SQL 统计，提高性能
2. **缓存机制**：对统计结果进行短时间缓存，减少重复计算
3. **批量查询**：优化预警详情的查询方式，减少数据库访问次数
