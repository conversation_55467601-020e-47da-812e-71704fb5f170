<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.risk.mapper.InspectIssuedMapper">
    
    <resultMap type="InspectIssued" id="InspectIssuedResult">
        <result property="id"    column="id"    />
        <result property="pitfallsId"    column="pitfalls_id"    />
        <result property="name"    column="name"    />
        <result property="type"    column="type"    />
        <result property="units"    column="units"    />
        <result property="unitId"    column="unit_id"    />
        <result property="project"    column="project"    />
        <result property="projectId"    column="project_id"    />
        <result property="areas"    column="areas"    />
        <result property="contens"    column="contens"    />
        <result property="requires"    column="requires"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="fields"    column="fields"    />
        <result property="endTime"    column="end_time"    />
        <result property="status"    column="status"    />
        <result property="taskProgress"    column="task_progress"    />
        <result property="remarks"    column="remarks"    />
        <result property="issuedUnit"    column="issued_unit"    />
        <result property="issuedUnitId"    column="issued_unit_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createById"    column="create_by_id"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectInspectIssuedVo">
        select id, pitfalls_id, name, type, units, unit_id, project, project_id, areas, contens, requires, file_url,
        fields, end_time, status, task_progress, remarks, issued_unit, issued_unit_id, create_by, create_by_id, update_time, del_flag
        from risk_inspect_issued
    </sql>

    <select id="selectInspectIssuedList" parameterType="InspectIssued" resultMap="InspectIssuedResult">
        <include refid="selectInspectIssuedVo"/>
        <where>  
            <if test="pitfallsId != null "> and pitfalls_id = #{pitfallsId}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="units != null  and units != ''"> and units = #{units}</if>
            <if test="unitId != null  and unitId != ''"> and unit_id = #{unitId}</if>
            <if test="project != null  and project != ''"> and project = #{project}</if>
            <if test="projectId != null  and projectId != ''"> and project_id = #{projectId}</if>
            <if test="areas != null  and areas != ''"> and areas = #{areas}</if>
            <if test="contens != null  and contens != ''"> and contens = #{contens}</if>
            <if test="requires != null  and requires != ''"> and requires = #{requires}</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
            <if test="fields != null  and fields != ''"> and fields = #{fields}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="remarks != null  and remarks != ''"> and remarks = #{remarks}</if>
            <if test="issuedUnit != null  and issuedUnit != ''"> and issued_unit = #{issuedUnit}</if>
            <if test="issuedUnitId != null "> and issued_unit_id = #{issuedUnitId}</if>
            <if test="createById != null "> and create_by_id = #{createById}</if>
        </where>
    </select>

    <select id="getProgress" resultType="com.tocc.risk.domain.InspectTask">
        select it.*, sd.dept_name unit from risk_inspect_task it
        left join sys_user su on it.informant_id = su.user_id
        left join sys_dept sd on su.dept_id = sd.dept_id
        where it.issued_id = #{id}
    </select>

    <resultMap id="bigDecimalMap" type="java.util.HashMap">
        <result property="num" column="num" javaType="java.math.BigDecimal"/>
        <result property="suc" column="suc" javaType="java.math.BigDecimal"/>
    </resultMap>

    <select id="sumProgress" resultMap="bigDecimalMap">
        select count(*) num,
        sum(case when status = 1 then 1 else 0 end) suc
        from risk_inspect_task where issued_id = #{id} and del_flag = 0
    </select>

    <select id="selectInspectIssuedById" resultMap="InspectIssuedResult">
        <include refid="selectInspectIssuedVo"/>
        where id = #{id}
    </select>

    <insert id="insertInspectIssued" parameterType="InspectIssued" keyProperty="id">
        insert into risk_inspect_issued
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="pitfallsId != null">pitfalls_id,</if>
            <if test="name != null">name,</if>
            <if test="type != null">type,</if>
            <if test="units != null">units,</if>
            <if test="unitId != null">unit_id,</if>
            <if test="project != null ">project,</if>
            <if test="projectId != null">project_id,</if>
            <if test="areas != null">areas,</if>
            <if test="contens != null">contens,</if>
            <if test="requires != null">requires,</if>
            <if test="fileUrl != null">file_url,</if>
            <if test="fields != null">fields,</if>
            <if test="endTime != null">end_time,</if>
            <if test="status != null">status,</if>
            <if test="taskProgress != null">task_progress,</if>
            <if test="remarks != null">remarks,</if>
            <if test="issuedUnit != null">issued_unit,</if>
            <if test="issuedUnitId != null">issued_unit_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createById != null">create_by_id,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="pitfallsId != null">#{pitfallsId},</if>
            <if test="name != null">#{name},</if>
            <if test="type != null">#{type},</if>
            <if test="units != null">#{units},</if>
            <if test="unitId != null">#{unitId},</if>
            <if test="project != null">#{project},</if>
            <if test="projectId != null">#{projectId},</if>
            <if test="areas != null">#{areas},</if>
            <if test="contens != null">#{contens},</if>
            <if test="requires != null">#{requires},</if>
            <if test="fileUrl != null">#{fileUrl},</if>
            <if test="fields != null">#{fields},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="status != null">#{status},</if>
            <if test="taskProgress != null">#{taskProgress},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="issuedUnit != null">#{issuedUnit},</if>
            <if test="issuedUnitId != null">#{issuedUnitId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createById != null">#{createById},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateInspectIssued" parameterType="InspectIssued">
        update risk_inspect_issued
        <trim prefix="SET" suffixOverrides=",">
            <if test="pitfallsId != null">pitfalls_id = #{pitfallsId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="type != null">type = #{type},</if>
            <if test="units != null">units = #{units},</if>
            <if test="unitId != null">unit_id = #{unitId},</if>
            <if test="project != null">project = #{project},</if>
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="areas != null">areas = #{areas},</if>
            <if test="contens != null">contens = #{contens},</if>
            <if test="requires != null">requires = #{requires},</if>
            <if test="fileUrl != null">file_url = #{fileUrl},</if>
            <if test="fields != null">fields = #{fields},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="taskProgress != null">task_progress = #{taskProgress},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="issuedUnit != null">issued_unit = #{issuedUnit},</if>
            <if test="issuedUnitId != null">issued_unit_id = #{issuedUnitId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createById != null">create_by_id = #{createById},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteInspectIssuedById" parameterType="Long">
        delete from risk_inspect_issued where id = #{id}
    </delete>

    <delete id="deleteInspectIssuedByIds" parameterType="String">
        delete from risk_inspect_issued where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getResult" resultType="java.util.Map">
        select ii.name, count(*) fxnum,
        sum(case when rp.is_pitfalls = 1 then 1 else 0 end) yhnum
        from risk_inspect_issued ii
        left join risk_inspect_task it on it.issued_id = ii.id
        left join risk_pitfalls rp on rp.task_id = it.id
        where ii.id = #{id}
    </select>

    <select id="getPitfallsList" resultType="com.tocc.risk.domain.Pitfalls">
        select rp.* from risk_pitfalls rp
        left join risk_inspect_task it on it.id = rp.task_id
        where it.issued_id = #{issuedId}
    </select>
</mapper>