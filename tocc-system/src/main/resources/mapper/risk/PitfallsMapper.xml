<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.risk.mapper.PitfallsMapper">
    
    <resultMap type="Pitfalls" id="PitfallsResult">
        <result property="id"    column="id"    />
        <result property="taskId"    column="task_id"    />
        <result property="contents"    column="contents"    />
        <result property="projectId"    column="projectId"    />
        <result property="name"    column="name"    />
        <result property="city"    column="city"    />
        <result property="district"    column="district"    />
        <result property="address"    column="address"    />
        <result property="lot"    column="lot"    />
        <result property="lat"    column="lat"    />
        <result property="units"    column="units"    />
        <result property="inspectType"    column="inspect_type"    />
        <result property="roadNum"    column="road_num"    />
        <result property="riskLevel"    column="risk_level"    />
        <result property="isPitfalls"    column="is_pitfalls"    />
        <result property="pileStart"    column="pile_start"    />
        <result property="pileEnd"    column="pile_end"    />
        <result property="isMeasure"    column="is_measure"    />
        <result property="measure"    column="measure"    />
        <result property="sceneImg"    column="scene_img"    />
        <result property="measureFiles"    column="measure_files"    />
        <result property="provinceUnit"    column="province_unit"    />
        <result property="provinceUnitId"    column="province_unit_id"    />
        <result property="reviewUnit"    column="review_unit"    />
        <result property="reviewUnitId"    column="review_unit_id"    />
        <result property="inspectUnit"    column="inspect_unit"    />
        <result property="inspectUnitId"    column="inspect_unit_id"    />
        <result property="remakes"    column="remakes"    />
        <result property="inspectTime"    column="inspect_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createById"    column="create_by_id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="issuedId"    column="issued_id"    />
        <result property="deptName"    column="dept_name"    />
    </resultMap>

    <sql id="selectPitfallsVo">
        select id, task_id, contents, project_id, name, city, district, address, lot, lat, units, inspect_type, road_num,
        risk_level, is_pitfalls, pile_start, pile_end, is_measure, measure, scene_img, measure_files, province_unit, province_unit_id,
        review_unit, review_unit_id, inspect_unit, inspect_unit_id, remakes, inspect_time, create_time, update_time, create_by, create_by_id, del_flag from risk_pitfalls
    </sql>

    <select id="selectPitfallsList" parameterType="Pitfalls" resultMap="PitfallsResult">
        select rp.*, it.issued_id, sd.dept_name from risk_inspect_task it
        left join risk_pitfalls rp on it.id = rp.task_id
        left join sys_user u on it.informant_id = u.user_id
        left join sys_dept sd on u.dept_id = sd.dept_id
        <where>
            rp.del_flag = 0
            <if test="taskId != null"> and rp.task_id = #{taskId}</if>
            <if test="contents != null  and contents != ''"> and rp.contents = #{contents}</if>
            <if test="name != null  and name != ''"> and rp.name = #{name}</if>
            <if test="city != null  and city != ''"> and rp.city = #{city}</if>
            <if test="projectId != null  and projectId != ''"> and rp.project_id = #{projectId}</if>
            <if test="district != null  and district != ''"> and rp.district = #{district}</if>
            <if test="address != null  and address != ''"> and rp.address = #{address}</if>
            <if test="lot != null  and lot != ''"> and rp.lot = #{lot}</if>
            <if test="lat != null  and lat != ''"> and rp.lat = #{lat}</if>
            <if test="units != null  and units != ''"> and rp.units = #{units}</if>
            <if test="inspectType != null  and inspectType != ''"> and rp.inspect_type = #{inspectType}</if>
            <if test="roadNum != null  and roadNum != ''"> and rp.road_num = #{roadNum}</if>
            <if test="riskLevel != null "> and rp.risk_level = #{riskLevel}</if>
            <if test="isPitfalls != null "> and rp.is_pitfalls = #{isPitfalls}</if>
            <if test="pileStart != null  and pileStart != ''"> and rp.pile_start = #{pileStart}</if>
            <if test="pileEnd != null  and pileEnd != ''"> and rp.pile_end = #{pileEnd}</if>
            <if test="isMeasure != null  and isMeasure != ''"> and rp.is_measure = #{isMeasure}</if>
            <if test="measure != null  and measure != ''"> and rp.measure = #{measure}</if>
            <if test="sceneImg != null  and sceneImg != ''"> and rp.scene_img = #{sceneImg}</if>
            <if test="inspectTime != null"> and rp.inspect_time = #{inspectTime}</if>
            <if test="measureFiles != null  and measureFiles != ''"> and rp.measure_files = #{measureFiles}</if>
            <if test="provinceUnit != null  and provinceUnit != ''"> and rp.province_unit = #{provinceUnit}</if>
            <if test="provinceUnitId != null  and provinceUnitId != ''"> and rp.province_unit_id = #{provinceUnitId}</if>
            <if test="reviewUnit != null  and reviewUnit != ''"> and rp.review_unit = #{reviewUnit}</if>
            <if test="reviewUnitId != null  and reviewUnitId != ''"> and rp.review_unit_id = #{reviewUnitId}</if>
            <if test="inspectUnit != null  and inspectUnit != ''"> and rp.inspect_unit = #{inspectUnit}</if>
            <if test="inspectUnitId != null  and inspectUnitId != ''"> and rp.inspect_unit_id = #{inspectUnitId}</if>
            <if test="remakes != null  and remakes != ''"> and rp.remakes = #{remakes}</if>
            <if test="createById != null "> and rp.create_by_id = #{createById}</if>
            <!-- 数据范围过滤 -->
            ${params.dataScope}
        </where>
        order by rp.create_time desc
    </select>
    
    <select id="selectPitfallsById" resultMap="PitfallsResult">
        <include refid="selectPitfallsVo"/>
        where id = #{id}
    </select>

    <insert id="insertPitfalls" parameterType="Pitfalls" keyProperty="id">
        insert into risk_pitfalls
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="taskId != null">task_id,</if>
            <if test="contents != null">contents,</if>
            <if test="projectId != null">project_id,</if>
            <if test="name != null">name,</if>
            <if test="city != null">city,</if>
            <if test="district != null">district,</if>
            <if test="address != null">address,</if>
            <if test="lot != null">lot,</if>
            <if test="lat != null">lat,</if>
            <if test="units != null">units,</if>
            <if test="inspectType != null">inspect_type,</if>
            <if test="roadNum != null">road_num,</if>
            <if test="riskLevel != null">risk_level,</if>
            <if test="isPitfalls != null">is_pitfalls,</if>
            <if test="pileStart != null">pile_start,</if>
            <if test="pileEnd != null">pile_end,</if>
            <if test="isMeasure != null">is_measure,</if>
            <if test="measure != null">measure,</if>
            <if test="sceneImg != null">scene_img,</if>
            <if test="measureFiles != null">measure_files,</if>
            <if test="provinceUnit != null">province_unit,</if>
            <if test="provinceUnitId != null">province_unit_id,</if>
            <if test="reviewUnit != null">review_unit,</if>
            <if test="reviewUnitId != null">review_unit_id,</if>
            <if test="inspectUnit != null">inspect_unit,</if>
            <if test="inspectUnitId != null">inspect_unit_id,</if>
            <if test="remakes != null">remakes,</if>
            <if test="inspectTime != null">inspect_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createById != null">create_by_id,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="isApprove != null">is_approve,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="taskId != null">#{taskId},</if>
            <if test="contents != null">#{contents},</if>
            <if test="projectId != null">#{projectId},</if>
            <if test="name != null">#{name},</if>
            <if test="city != null">#{city},</if>
            <if test="district != null">#{district},</if>
            <if test="address != null">#{address},</if>
            <if test="lot != null">#{lot},</if>
            <if test="lat != null">#{lat},</if>
            <if test="units != null">#{units},</if>
            <if test="inspectType != null">#{inspectType},</if>
            <if test="roadNum != null">#{roadNum},</if>
            <if test="riskLevel != null">#{riskLevel},</if>
            <if test="isPitfalls != null">#{isPitfalls},</if>
            <if test="pileStart != null">#{pileStart},</if>
            <if test="pileEnd != null">#{pileEnd},</if>
            <if test="isMeasure != null">#{isMeasure},</if>
            <if test="measure != null">#{measure},</if>
            <if test="sceneImg != null">#{sceneImg},</if>
            <if test="measureFiles != null">#{measureFiles},</if>
            <if test="provinceUnit != null">#{provinceUnit},</if>
            <if test="provinceUnitId != null">#{provinceUnitId},</if>
            <if test="reviewUnit != null">#{reviewUnit},</if>
            <if test="reviewUnitId != null">#{reviewUnitId},</if>
            <if test="inspectUnit != null">#{inspectUnit},</if>
            <if test="inspectUnitId != null">#{inspectUnitId},</if>
            <if test="remakes != null">#{remakes},</if>
            <if test="inspectTime != null">#{inspectTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createById != null">#{createById},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="isApprove != null">#{isApprove},</if>
         </trim>
    </insert>

    <update id="updatePitfalls" parameterType="Pitfalls">
        update risk_pitfalls
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="contents != null">contents = #{contents},</if>
            <if test="projectId != null">project_d = #{projectId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="city != null">city = #{city},</if>
            <if test="district != null">district = #{district},</if>
            <if test="address != null">address = #{address},</if>
            <if test="lot != null">lot = #{lot},</if>
            <if test="lat != null">lat = #{lat},</if>
            <if test="units != null">units = #{units},</if>
            <if test="inspectType != null">inspect_type = #{inspectType},</if>
            <if test="roadNum != null">road_num = #{roadNum},</if>
            <if test="riskLevel != null">risk_level = #{riskLevel},</if>
            <if test="isPitfalls != null">is_pitfalls = #{isPitfalls},</if>
            <if test="pileStart != null">pile_start = #{pileStart},</if>
            <if test="pileEnd != null">pile_end = #{pileEnd},</if>
            <if test="isMeasure != null">is_measure = #{isMeasure},</if>
            <if test="measure != null">measure = #{measure},</if>
            <if test="sceneImg != null">scene_img = #{sceneImg},</if>
            <if test="measureFiles != null">measure_files = #{measureFiles},</if>
            <if test="provinceUnit != null">province_unit = #{provinceUnit},</if>
            <if test="provinceUnitId != null">province_unit_id = #{provinceUnitId},</if>
            <if test="reviewUnit != null">review_unit = #{reviewUnit},</if>
            <if test="reviewUnitId != null">review_unit_id = #{reviewUnitId},</if>
            <if test="inspectUnit != null">inspect_unit = #{inspectUnit},</if>
            <if test="inspectUnitId != null">inspect_unit_id = #{inspectUnitId},</if>
            <if test="remakes != null">remakes = #{remakes},</if>
            <if test="inspectTime != null">inspect_time = #{inspectTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createById != null">create_by_id = #{createById},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="isApprove != null">is_approve = #{isApprove},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePitfallsById">
        delete from risk_pitfalls where id = #{id}
    </delete>

    <delete id="deletePitfallsByIds" parameterType="String">
        delete from risk_pitfalls where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getOverallSituation" resultType="com.tocc.risk.vo.WordFileVo">
        select count(*) overalls,
        sum(case when rp.risk_level = 1 then 1 else 0 end) zd,
        sum(case when rp.risk_level = 2 then 1 else 0 end) yb,
        sum(case when mt.status = 2 then 1 else 0 end) isZg,
        sum(case when mt.status != 2 then 1 else 0 end) noZg,
        sum(case when mt.status != 2 and rp.risk_level = 1 then 1 else 0 end) zdNoZg,
        sum(case when mt.status != 2 and rp.risk_level = 2 then 1 else 0 end) ybNoZg
        from risk_pitfalls rp
        left join risk_modify_task mt on mt.pitfalls_id = rp.id
        where rp.is_pitfalls = 1 and rp.del_flag = 0
    </select>

    <select id="getCitysSpread" resultType="com.tocc.risk.vo.WordFileVo">
        select city, count(*) cityAlls,
        sum(case when rp.risk_level = 1 then 1 else 0 end) cityZd
        from risk_pitfalls rp where rp.is_pitfalls = 1 and rp.del_flag = 0
        group by rp.city order by cityAlls desc
    </select>

    <select id="getAreasSpread" resultType="com.tocc.risk.vo.WordFileVo">
        select #{title} areas, count(*) areasAlls,
        GROUP_CONCAT(rp.units) units,
        sum(case when rp.inspect_type = '1' then 1 else 0 end) zrzhfx,
        sum(case when rp.inspect_type = '2' then 1 else 0 end) rwfx
        from risk_pitfalls rp
        left join risk_inspect_task it on rp.task_id = it.id
        where rp.is_pitfalls = 1 and it.issued_id in (
        select id from risk_inspect_issued where areas like concat('%', #{val}, '%') )
    </select>

    <select id="getProgress" resultType="com.tocc.risk.vo.WordFileVo">
        select
        count(*) speedAlls,
        sum(case when mt.status = 2 then 1 else 0 end) succ,
        sum(case when rp.risk_level = 1 and mt.status = 2 then 1 else 0 end) huge,
        sum(case when rp.risk_level = 2 and mt.status = 2 then 1 else 0 end) pub,
        sum(case when mt.status != 2 then 1 else 0 end) pau,
        sum(case when rp.risk_level = 1 and mt.status != 2 then 1 else 0 end) pauh,
        sum(case when rp.risk_level = 2 and mt.status != 2 then 1 else 0 end) paup
        from risk_pitfalls rp
        left join risk_modify_task mt on mt.pitfalls_id = rp.id
        where rp.is_pitfalls = 1 and rp.del_flag = 0
    </select>

    <select id="getCityNotChange" resultType="com.tocc.risk.vo.WordFileVo">
        select rp.city recCity,
        sum(case when mt.status != 2 then 1 else 0 end) notRec
        from risk_pitfalls rp
        left join risk_modify_task mt on mt.pitfalls_id = rp.id
        where rp.is_pitfalls = 1 and rp.del_flag = 0 group by rp.city order by notRec desc limit 3
    </select>

    <select id="getPitfallsByTaskId" resultMap="PitfallsResult">
        select rp.* from risk_inspect_task it left join risk_pitfalls rp on rp.task_id = it.id
        where it.id = #{taskId} and rp.del_flag = 0
    </select>
</mapper>