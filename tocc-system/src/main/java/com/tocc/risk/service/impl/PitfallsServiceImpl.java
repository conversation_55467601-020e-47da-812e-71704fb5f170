package com.tocc.risk.service.impl;

import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

import cn.hutool.core.util.IdUtil;
import com.tocc.common.annotation.DataScope;
import com.tocc.common.core.domain.entity.SysDictData;
import com.tocc.common.core.domain.model.LoginUser;
import com.tocc.common.exception.ServiceException;
import com.tocc.common.utils.DateUtils;
import com.tocc.common.utils.DictUtils;
import com.tocc.common.utils.SecurityUtils;
import com.tocc.common.utils.StringUtils;
import com.tocc.common.utils.file.WordUtils;
import com.tocc.risk.domain.Approve;
import com.tocc.risk.domain.InspectIssued;
import com.tocc.risk.domain.InspectTask;
import com.tocc.risk.mapper.ApproveMapper;
import com.tocc.risk.mapper.InspectIssuedMapper;
import com.tocc.risk.mapper.InspectTaskMapper;
import com.tocc.risk.vo.WordFileVo;
import lombok.SneakyThrows;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import com.tocc.risk.mapper.PitfallsMapper;
import com.tocc.risk.domain.Pitfalls;
import com.tocc.risk.service.IPitfallsService;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;

/**
 * 隐患列Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Service
public class PitfallsServiceImpl implements IPitfallsService {
    public static final Logger log = LoggerFactory.getLogger(PitfallsServiceImpl.class);

    @Autowired
    private PitfallsMapper pitfallsMapper;
    @Autowired
    private InspectTaskMapper inspectTaskMapper;
    @Autowired
    private InspectIssuedMapper inspectIssuedMapper;
    @Autowired
    private ApproveMapper approveMapper;
    @Autowired
    private IAlarmService alarmService;



    /**
     * 查询隐患列
     * 
     * @param id 隐患列主键
     * @return 隐患列
     */
    @Override
    public Pitfalls selectPitfallsById(String id)
    {
        return pitfallsMapper.selectPitfallsById(id);
    }

    /**
     * 查询隐患列列表
     *
     * @param pitfalls 隐患列
     * @return 隐患列
     */
    @Override
    @DataScope(deptAlias = "u")
    public List<Pitfalls> selectPitfallsList(Pitfalls pitfalls)
    {
//        if (StringUtils.isNotEmpty(pitfalls.getSpecial())) {
//            String[] ids = pitfalls.getSpecial().split("-");
//        }
        return pitfallsMapper.selectPitfallsList(pitfalls);
    }

    /**
     * 任务填报
     * 
     * @param pitfalls 隐患列
     * @return 结果
     */
    @Override
    @Transactional
    public int insertPitfalls(Pitfalls pitfalls)
    {
        InspectTask task = inspectTaskMapper.selectInspectTaskById(pitfalls.getId());
        task.setStatus(1);
        task.setUpdateTime(DateUtils.getNowDate());
        inspectTaskMapper.updateInspectTask(task);
        // 计算任务完成进度
        setTaskProgress(task.getIssuedId());

        // 当前登录者就是排查单位
        Long userId = SecurityUtils.getUserId();
        pitfalls.setInspectUnitId(userId.toString());
        // 所属单位设置
        pitfalls.setUnits(SecurityUtils.getDeptId().toString());

        pitfalls.setDelFlag(0);
        pitfalls.setId(null);
        pitfalls.setTaskId(task.getId());
        if (StringUtils.isNotEmpty(task.getProjectId())) {
            pitfalls.setProjectId(task.getProjectId());
        }
        pitfalls.setCreateTime(DateUtils.getNowDate());
        pitfalls.setId(IdUtil.fastSimpleUUID());
        pitfalls.setIsApprove(0);
        pitfallsMapper.insertPitfalls(pitfalls);

        // 隐患告警通知
        setAlarmInfo(pitfalls);

        // 检查审批数据录入
        Approve approve = new Approve();
        // 填报之后是第一步审批
        approve.setStep("1");
        approve.setTaskId(task.getId());

        // 获取任务下发ID
        InspectIssued issued = inspectIssuedMapper.selectInspectIssuedById(task.getIssuedId());
        approve.setIssuedId(issued.getId());
        approve.setPitfallsId(pitfalls.getId());
        approve.setApproveById(pitfalls.getReviewUnitId());
        // 待处理
        // 走审核的话需要改成  0
        approve.setStatus(0);
        approve.setApproveTime(new Date());
        approve.setId(IdUtil.fastSimpleUUID());
        return approveMapper.insertApprove(approve);

    }

    /**
     * 隐患告警通知
     * @param pitfalls
     */
    @Transactional
    private void setAlarmInfo(Pitfalls pitfalls) {
        // 判断是否是隐患点，是隐患点，则创建告警信息
        if (pitfalls.getIsPitfalls() == 1) {
            String title = "触发了一条新的隐患信息：" + pitfalls.getName();
            // 创建告警记录
            AlarmInfoDTO alarmInfo = new AlarmInfoDTO();
            alarmInfo.setAlarmId(IdUtil.fastSimpleUUID()); // 生成唯一告警ID
            alarmInfo.setAlarmType("1"); // 信息更新超时对应字典值1
            alarmInfo.setAlarmSubtype("1"); // 设置告警子类型
            alarmInfo.setAlarmTitle(title);
            alarmInfo.setAlarmContent(pitfalls.getContents());
            alarmInfo.setAlarmLevel(pitfalls.getRiskLevel() + ""); // 中等级别
            alarmInfo.setSourceType("1"); // 源数据类型
            alarmInfo.setSourceId(pitfalls.getId() + ""); // 源数据ID

            // 获取创建人的部门信息
            LoginUser loginUser = SecurityUtils.getLoginUser();
            alarmInfo.setOrgId(loginUser.getDeptId() + ""); // 设置组织ID
            alarmInfo.setOrgName(loginUser.getUser().getDept().getDeptName()); // 设置组织名称
            alarmInfo.setAdministrativeAreaId("default"); // 设置默认行政区划ID
            alarmInfo.setAlarmTime(new Date());
            // 走审核的话需要改成  0
            alarmInfo.setStatus("1"); // 未处理
            alarmInfo.setCreateBy(loginUser.getUser().getNickName()); // 系统创建
            alarmInfo.setCreateTime(new Date());

            alarmService.createAlarm(alarmInfo);
        }
    }

    /**
     * 计算下发任务的完成进度
     * @param issuedId
     */
    @Transactional
    private void setTaskProgress(String issuedId) {
        Map<String, BigDecimal> map = inspectIssuedMapper.sumProgress(issuedId);
        BigDecimal sum = map.get("suc").divide(map.get("num"), 2, BigDecimal.ROUND_HALF_UP);
        String progress = sum.multiply(BigDecimal.valueOf(100)) + "%";
        InspectIssued issued = new InspectIssued();
        issued.setId(issuedId);
        issued.setTaskProgress(progress);
        inspectIssuedMapper.updateInspectIssued(issued);
    }

    /**
     * 修改隐患列
     * 
     * @param pitfalls 隐患列
     * @return 结果
     */
    @Override
    public int updatePitfalls(Pitfalls pitfalls)
    {
        pitfalls.setUpdateTime(DateUtils.getNowDate());
        return pitfallsMapper.updatePitfalls(pitfalls);
    }

    /**
     * 批量删除隐患列
     * 
     * @param ids 需要删除的隐患列主键
     * @return 结果
     */
    @Override
    public int deletePitfallsByIds(Long[] ids)
    {
        return pitfallsMapper.deletePitfallsByIds(ids);
    }

    /**
     * 删除隐患列信息
     * 
     * @param id 隐患列主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deletePitfallsById(String id)
    {
        Pitfalls p = pitfallsMapper.selectPitfallsById(id);
        InspectTask task = inspectTaskMapper.selectInspectTaskById(p.getTaskId());
        task.setStatus(0);
        inspectTaskMapper.updateInspectTask(task);
        return pitfallsMapper.deletePitfallsById(id);
    }

    @Override
    @SneakyThrows
    public void exportAnalysis(HttpServletResponse response, Pitfalls pitfalls) {
        Map<String, String> params = new HashMap<>();
        params.put("#{nowDate}", DateUtils.dateTimeNow("yyyy年MM月dd日"));

        // 查询隐患总体情况统计
        WordFileVo overVo = pitfallsMapper.getOverallSituation();
        // 计算总体占比
        setOverZb(overVo, params);


        // 隐患地域分布情况
        List<WordFileVo> cityList = pitfallsMapper.getCitysSpread();
        setCityZb(cityList, params);


        // 隐患类型分析
        List<SysDictData> dictList = DictUtils.getDictCache("area_type");
        List<WordFileVo> areaList = new ArrayList<>();
        for (SysDictData dic : dictList) {
            WordFileVo areas =  pitfallsMapper.getAreasSpread(dic.getDictLabel(), dic.getDictValue());
            areaList.add(areas);
        }
        setTypeZb(areaList, params);


        // 整改进展情况
        WordFileVo ProgVo =  pitfallsMapper.getProgress();
        setProgress(ProgVo, params);

        // 未整改各市数据及占比
        List<WordFileVo> notList = pitfallsMapper.getCityNotChange();
        setNotCorrection(notList, params);


        // 遍历文档中的所有段落并替换文本
        try (
                InputStream resourceAsStream = getClass().getClassLoader().getResourceAsStream("templates/风险隐患情况分析报告模板.docx");
                BufferedInputStream bos = new BufferedInputStream(resourceAsStream);
                XWPFDocument document = new XWPFDocument(bos)
        ) {
            for (XWPFParagraph paragraph : document.getParagraphs()) {
                WordUtils.replaceInParagraph(paragraph, params);
            }
            // 响应修改后的文档
            String fileName = "风险隐患情况分析报告.docx";
            String trueName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.name());
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + trueName);
            document.write(response.getOutputStream());
        } catch (IOException ex) {
            log.error("风险隐患情况分析报告文档导出异常: [{}]", ex.getMessage(), ex);
            throw new ServiceException("风险隐患情况分析报告文档导出异常");
        }
    }


    /**
     * 获取隐患总体情况占比
     * @return
     */
    private Map<String, String> setOverZb(WordFileVo overVo, Map<String, String> params) {
        BigDecimal sum = new BigDecimal(100);
        // 重大隐患占比
        BigDecimal zdzb = overVo.getZd().divide(overVo.getOveralls(), 2, BigDecimal.ROUND_HALF_UP);
        // 一般隐患占比
        BigDecimal ybZb = overVo.getYb().divide(overVo.getOveralls(), 2, BigDecimal.ROUND_HALF_UP);
        // 总体整改率
        BigDecimal isZgzb = overVo.getIsZg().divide(overVo.getOveralls(), 2, BigDecimal.ROUND_HALF_UP);

        // 填装替换模板的数据
        params.put("#{overalls}", overVo.getOveralls().toString());
        params.put("#{zd}", overVo.getZd().toString());
        params.put("#{zdzb}", zdzb.multiply(sum).toString());
        params.put("#{yb}", overVo.getYb().toString());
        params.put("#{ybzb}", ybZb.multiply(sum).toString());
        params.put("#{isZg}", overVo.getIsZg().toString());
        params.put("#{isZgzb}", isZgzb.multiply(sum).toString());
        params.put("#{noZg}", overVo.getNoZg().toString());
        params.put("#{zdNoZg}", overVo.getZdNoZg().toString());
        params.put("#{ybNoZg}", overVo.getYbNoZg().toString());
        return params;
    }

    /**
     * 计算城市占比
     * @param cityList
     * @return
     */
    private Map<String, String> setCityZb(List<WordFileVo> cityList, Map<String, String> params) {
        BigDecimal sum = new BigDecimal(100);
        BigDecimal otherAlls = BigDecimal.ZERO;
        BigDecimal otherZd = BigDecimal.ZERO;
        BigDecimal all = BigDecimal.valueOf(Long.parseLong(params.get("#{overalls}")));
        String contens = "";
        for (int i = 0; i < cityList.size(); i++) {
            WordFileVo vo = cityList.get(i);
            // 只要前三个
            if (i < 3) {
                // 计算占比
                BigDecimal zb = vo.getCityAlls().divide(all, 2, BigDecimal.ROUND_HALF_UP);
                if (i == 0) {
                    contens += vo.getCity() + "：共排查隐患" +
                            vo.getCityZd().toString() + "项（重大隐患"+
                            vo.getCityZd().toString() + "项），占全区隐患总数的" +
                            zb.multiply(sum).toString() + "%，位列第一；\n";
                } else {
                    contens += "    " + vo.getCity() + "：共排查隐患" +
                            vo.getCityZd().toString() + "项（重大隐患" +
                            vo.getCityZd().toString() + "项），占全区隐患总数的" +
                            zb.multiply(sum).toString() + "%；\n";
                }
            } else {
                // 其他城市隐患数
                otherZd.add(vo.getCityZb());
                otherAlls.add(vo.getCityAlls());
            }
        }
        // 其他城市隐患总和
        contens += "    其余地市隐患数量相对较少，合计" +
                otherAlls.toString() + "项（重大隐患" +
                otherZd.toString() + "项）。";

        params.put("#{city}", contens);

        return params;
    }

    /**
     * 计算隐患类型占比
     * @param areaList
     * @param params
     * @return
     */
    private Map<String, String> setTypeZb(List<WordFileVo> areaList, Map<String, String> params) {
        // 对数据进行排序
        areaList.sort(Comparator.comparing(WordFileVo::getAreasAlls).reversed());
        BigDecimal sum = new BigDecimal(100);
        BigDecimal otherAlls = BigDecimal.ZERO;
        String contens = "";
        BigDecimal all = BigDecimal.valueOf(Long.parseLong(params.get("#{overalls}")));
        for (int i = 0; i < areaList.size(); i++) {
            WordFileVo vo = areaList.get(i);
            if (i < 3) {
                // 计算占比
                BigDecimal zb = vo.getAreasAlls().divide(all, 2, BigDecimal.ROUND_HALF_UP);
                String zrzh = vo.getZrzhfx() == null ? "0" : vo.getZrzhfx().toString();
                String refx = vo.getRwfx() == null ? "0" : vo.getRwfx().toString();
                contens += vo.getAreas() + "领域（共" +
                        vo.getAreasAlls().toString() + "项，占比" +
                        zb.multiply(sum).toString() + "%，责任单位：" +
                        vo.getUnits() + "）：自然灾害风险（" +
                        zrzh + "项）、人为风险（" +
                        refx + "项）。\n    ";
            } else {
                // 其他领域隐患数
                otherAlls.add(vo.getAreasAlls());
            }
        }
        // 其他城市隐患占比
        BigDecimal zb = otherAlls.divide(all, 2, BigDecimal.ROUND_HALF_UP);
        contens += "其他领域合计" + otherAlls.toString() + "项（占比" + zb.multiply(sum).toString() + "%）。";

        params.put("#{areas}", contens);
        return params;
    }

    /**
     * 计算整改进展情况占比
     * @param vo
     * @param params
     * @return
     */
    private Map<String, String> setProgress(WordFileVo vo, Map<String, String> params) {
        BigDecimal sum = new BigDecimal(100);
        BigDecimal all = BigDecimal.valueOf(Long.parseLong(params.get("#{overalls}")));

        // 数据填装
        params.put("#{succ}", vo.getSucc().toString());
        params.put("#{huge}", vo.getHuge().toString());
        params.put("#{pub}", vo.getPub().toString());
        // 计算整改概率
        BigDecimal zgl = vo.getSucc().divide(all, 2, BigDecimal.ROUND_HALF_UP);
        params.put("#{succZb}", zgl.multiply(sum).toString());
        params.put("#{pau}", vo.getPau().toString());
        params.put("#{pauh}", vo.getPauh().toString());
        // 计算重大未整改占比
        BigDecimal pauhZb = vo.getPauh().divide(all, 2, BigDecimal.ROUND_HALF_UP);
        params.put("#{pauhZb}", pauhZb.multiply(sum).toString());
        params.put("#{paup}", vo.getPaup().toString());
        // 计算一般未整改
        BigDecimal paupZb = vo.getPaup().divide(all, 2, BigDecimal.ROUND_HALF_UP);
        params.put("#{paupZb}", paupZb.multiply(sum).toString());

        return params;
    }

    private Map<String, String> setNotCorrection(List<WordFileVo> notList, Map<String, String> params) {
        String contens = "";
        for (int i = 0; i < notList.size(); i++) {
            WordFileVo vo = notList.get(i);
            contens += vo.getRecCity() + "（" + vo.getNotRec().toString() + "项）";
        }
        contens += "占比较高，需重点督导。";
        params.put("#{recCity}", contens);
        return params;
    }

}
