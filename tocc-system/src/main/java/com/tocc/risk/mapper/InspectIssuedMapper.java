package com.tocc.risk.mapper;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.tocc.risk.domain.InspectIssued;
import com.tocc.risk.domain.InspectTask;
import com.tocc.risk.domain.Pitfalls;

/**
 * 检查下发Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
public interface InspectIssuedMapper 
{
    /**
     * 查询检查下发
     * 
     * @param id 检查下发主键
     * @return 检查下发
     */
    public InspectIssued selectInspectIssuedById(String id);

    /**
     * 查询检查下发列表
     * 
     * @param inspectIssued 检查下发
     * @return 检查下发集合
     */
    public List<InspectIssued> selectInspectIssuedList(InspectIssued inspectIssued);

    public List<InspectTask> getProgress(String id);

    public Map<String, BigDecimal> sumProgress(String id);
    /**
     * 新增检查下发
     * 
     * @param inspectIssued 检查下发
     * @return 结果
     */
    public int insertInspectIssued(InspectIssued inspectIssued);

    /**
     * 修改检查下发
     * 
     * @param inspectIssued 检查下发
     * @return 结果
     */
    public int updateInspectIssued(InspectIssued inspectIssued);

    /**
     * 删除检查下发
     * 
     * @param id 检查下发主键
     * @return 结果
     */
    public int deleteInspectIssuedById(String id);

    /**
     * 批量删除检查下发
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInspectIssuedByIds(Long[] ids);

    public Map getResult(String id);

    public List<Pitfalls> getPitfallsList(String issuedId);
}
