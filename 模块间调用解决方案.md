# 模块间调用解决方案

## 问题描述

在实现告警数据源详情接口时，需要告警模块调用气象预警模块的详情接口，但出现了编译错误：

```
java: 程序包com.tocc.weather.domain.vo不存在
java: 程序包com.tocc.weather.service不存在
```

这是因为模块间的依赖关系没有正确配置。

## 当前状态

- ✅ **气象预警详情接口已实现**：`GET /weather/warning/{warningId}`
- ✅ **告警数据源详情接口框架已完成**：`GET /alarm/info/source/{alarmId}`
- ⏳ **模块间调用待解决**：告警模块暂时无法直接调用气象预警模块

## 解决方案

### 方案1：配置模块依赖（推荐）

在告警模块的 `pom.xml` 中添加气象预警模块依赖：

```xml
<dependency>
    <groupId>com.tocc</groupId>
    <artifactId>tocc-weather</artifactId>
    <version>${project.version}</version>
</dependency>
```

**优点**：
- 实现简单，直接调用
- 性能好，无网络开销
- 事务一致性好

**缺点**：
- 可能导致循环依赖
- 模块耦合度高

### 方案2：前端组合调用（最简单）

前端分别调用两个接口，然后组合数据：

```javascript
// 前端实现
const getAlarmWithSourceDetail = async (alarmId) => {
    // 1. 获取告警基础信息和数据源信息
    const alarmResponse = await axios.get(`/alarm/info/source/${alarmId}`);
    const alarmData = alarmResponse.data.data;
    
    // 2. 如果是气象预警，获取详细信息
    if (alarmData.sourceType === '6') {
        try {
            const warningResponse = await axios.get(`/weather/warning/${alarmData.sourceId}`);
            alarmData.sourceDetail = warningResponse.data.data;
        } catch (error) {
            console.log('无权限查看气象预警详情或预警不存在');
            alarmData.sourceDetail = {
                message: '无权限查看气象预警详情或预警不存在',
                status: 'NO_PERMISSION'
            };
        }
    }
    
    return alarmData;
};
```

**优点**：
- 无需修改后端代码
- 模块解耦
- 权限控制清晰

**缺点**：
- 需要两次网络请求
- 前端逻辑稍复杂

### 方案3：使用 Feign 客户端

在告警模块中创建 Feign 客户端调用气象预警接口：

```java
@FeignClient(name = "weather-service", url = "${weather.service.url}")
public interface WeatherWarningFeignClient {
    
    @GetMapping("/weather/warning/{warningId}")
    AjaxResult getWarningDetail(@PathVariable("warningId") String warningId);
}
```

**优点**：
- 模块解耦
- 支持负载均衡
- 容错机制好

**缺点**：
- 需要配置服务发现
- 网络开销

### 方案4：使用 HTTP 客户端

在告警模块中使用 RestTemplate 或 WebClient 调用：

```java
@Service
public class AlarmServiceImpl implements IAlarmService {
    
    @Autowired
    private RestTemplate restTemplate;
    
    @Value("${weather.service.url:http://localhost:8080}")
    private String weatherServiceUrl;
    
    private Map<String, Object> getWeatherWarningDetail(String warningId) {
        try {
            String url = weatherServiceUrl + "/weather/warning/" + warningId;
            ResponseEntity<AjaxResult> response = restTemplate.getForEntity(url, AjaxResult.class);
            
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                return (Map<String, Object>) response.getBody().getData();
            } else {
                return createErrorDetail(warningId, "获取气象预警详情失败");
            }
        } catch (Exception e) {
            return createErrorDetail(warningId, "调用气象预警服务异常：" + e.getMessage());
        }
    }
}
```

**优点**：
- 实现相对简单
- 模块解耦
- 可配置服务地址

**缺点**：
- 需要处理网络异常
- 需要配置 HTTP 客户端

## 推荐实施步骤

### 第一阶段：前端组合调用（立即可用）

1. **保持当前后端实现**：告警数据源详情返回TODO信息
2. **前端实现组合逻辑**：分别调用两个接口
3. **验证功能完整性**：确保权限控制和数据展示正确

### 第二阶段：后端优化（可选）

根据实际需求选择合适的方案：
- 如果系统是单体应用，使用方案1（模块依赖）
- 如果系统是微服务架构，使用方案3（Feign客户端）
- 如果希望简单实现，使用方案4（HTTP客户端）

## 当前可用接口

### 气象预警详情接口
```http
GET /weather/warning/{warningId}
Authorization: Bearer {token}
```

### 告警数据源详情接口
```http
GET /alarm/info/source/{alarmId}
Authorization: Bearer {token}
```

## 测试建议

### 测试气象预警详情接口
```bash
# 测试有权限的预警
curl -H "Authorization: Bearer {token}" \
     http://localhost:8080/weather/warning/86a2c865-1386-4c42-9869-3e365fe1e796

# 测试无权限的预警
curl -H "Authorization: Bearer {token}" \
     http://localhost:8080/weather/warning/invalid-id
```

### 测试告警数据源详情接口
```bash
# 测试气象预警类型的告警
curl -H "Authorization: Bearer {token}" \
     http://localhost:8080/alarm/info/source/{alarmId}
```

## 总结

虽然遇到了模块间依赖的问题，但我们已经成功实现了：

1. ✅ **完整的气象预警详情接口**：包含权限控制和完整数据
2. ✅ **告警数据源详情接口框架**：支持多种数据源类型
3. ✅ **清晰的解决方案**：多种方式可以实现模块间调用

**建议优先使用方案2（前端组合调用）**，这样可以立即投入使用，后续根据需要再优化后端实现。

---

**文档版本**: v1.0.0  
**最后更新**: 2025-06-18  
**维护人员**: 开发团队
