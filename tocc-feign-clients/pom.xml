<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    
    <parent>
        <groupId>com.tocc</groupId>
        <artifactId>tocc</artifactId>
        <version>3.8.8</version>
    </parent>
    
    <artifactId>tocc-feign-clients</artifactId>
    <packaging>jar</packaging>
    
    <description>
        Feign客户端模块，用于统一管理各模块间的远程调用
    </description>
    
    <dependencies>
        <!-- Spring Boot Starter Web -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        
        <!-- Spring Cloud OpenFeign -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        
        <!-- Spring Cloud LoadBalancer -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-loadbalancer</artifactId>
        </dependency>
        
        <!-- 公共模块 -->
        <dependency>
            <groupId>com.tocc</groupId>
            <artifactId>tocc-common</artifactId>
        </dependency>
        
        <!-- 系统模块（用于AjaxResult等公共类） -->
        <dependency>
            <groupId>com.tocc</groupId>
            <artifactId>tocc-system</artifactId>
        </dependency>
        
        <!-- 气象预警模块的VO类（仅编译时依赖） -->
        <dependency>
            <groupId>com.tocc</groupId>
            <artifactId>tocc-weather</artifactId>
            <scope>provided</scope>
        </dependency>
        
        <!-- Jackson for JSON处理 -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        
        <!-- Validation -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        
        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>
    
</project>
