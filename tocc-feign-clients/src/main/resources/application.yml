# Feign 客户端配置
feign:
  client:
    config:
      default:
        connectTimeout: 5000
        readTimeout: 10000
        loggerLevel: basic
      weather-service:
        connectTimeout: 5000
        readTimeout: 15000
        loggerLevel: full
  # 启用断路器
  circuitbreaker:
    enabled: true

# 服务地址配置
feign:
  weather:
    url: ${WEATHER_SERVICE_URL:http://localhost:8080}
  emergency:
    url: ${EMERGENCY_SERVICE_URL:http://localhost:8080}
  system:
    url: ${SYSTEM_SERVICE_URL:http://localhost:8080}

# 断路器配置
resilience4j:
  circuitbreaker:
    instances:
      weather-service:
        failure-rate-threshold: 50
        wait-duration-in-open-state: 30s
        sliding-window-size: 10
        minimum-number-of-calls: 5

# 日志配置
logging:
  level:
    com.tocc.feign: DEBUG
    feign: DEBUG
