package com.tocc.feign.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

/**
 * RestTemplate 配置
 * 
 * <AUTHOR>
 */
@Configuration
public class RestTemplateConfig {

    /**
     * 创建 RestTemplate Bean
     * 用于 Feign 客户端的 HTTP 调用
     */
    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }
}
