package com.tocc.feign.config;

import feign.Response;
import feign.codec.ErrorDecoder;
import lombok.extern.slf4j.Slf4j;

/**
 * Feign 错误解码器
 * 
 * <AUTHOR>
 */
@Slf4j
public class FeignErrorDecoder implements ErrorDecoder {

    @Override
    public Exception decode(String methodKey, Response response) {
        log.error("Feign调用失败: method={}, status={}, reason={}", 
                methodKey, response.status(), response.reason());
        
        switch (response.status()) {
            case 400:
                return new RuntimeException("请求参数错误");
            case 401:
                return new RuntimeException("未授权访问");
            case 403:
                return new RuntimeException("权限不足");
            case 404:
                return new RuntimeException("资源不存在");
            case 500:
                return new RuntimeException("服务内部错误");
            case 503:
                return new RuntimeException("服务不可用");
            default:
                return new RuntimeException("调用远程服务失败: " + response.reason());
        }
    }
}
