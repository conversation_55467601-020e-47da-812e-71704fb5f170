package com.tocc.feign.config;

import feign.Logger;
import feign.Request;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import feign.codec.ErrorDecoder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import com.tocc.common.utils.StringUtils;

import javax.servlet.http.HttpServletRequest;

/**
 * Feign 全局配置
 * 
 * <AUTHOR>
 */
@Configuration
public class FeignConfig {

    /**
     * Feign 日志级别
     */
    @Bean
    public Logger.Level feignLoggerLevel() {
        return Logger.Level.BASIC;
    }

    /**
     * 请求超时配置
     */
    @Bean
    public Request.Options options() {
        return new Request.Options(5000, 10000);
    }

    /**
     * 请求拦截器 - 传递认证信息
     */
    @Bean
    public RequestInterceptor requestInterceptor() {
        return new RequestInterceptor() {
            @Override
            public void apply(RequestTemplate template) {
                // 获取当前请求的认证信息
                ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
                if (attributes != null) {
                    HttpServletRequest request = attributes.getRequest();
                    
                    // 传递 Authorization 头
                    String authorization = request.getHeader("Authorization");
                    if (StringUtils.isNotEmpty(authorization)) {
                        template.header("Authorization", authorization);
                    }
                    
                    // 传递其他必要的头信息
                    String contentType = request.getHeader("Content-Type");
                    if (StringUtils.isNotEmpty(contentType)) {
                        template.header("Content-Type", contentType);
                    }
                }
            }
        };
    }

    /**
     * 错误解码器
     */
    @Bean
    public ErrorDecoder errorDecoder() {
        return new FeignErrorDecoder();
    }
}
