package com.tocc.feign.weather;

import com.tocc.common.core.domain.AjaxResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 气象预警 Feign 客户端降级处理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class WeatherWarningFeignClientFallback implements WeatherWarningFeignClient {

    @Override
    public AjaxResult getWarningDetail(String warningId) {
        log.warn("气象预警服务调用失败，执行降级处理: warningId={}", warningId);

        // 创建降级响应数据
        Map<String, Object> fallbackData = new HashMap<>();
        fallbackData.put("warningId", warningId);
        fallbackData.put("message", "气象预警服务暂时不可用，请稍后重试");
        fallbackData.put("status", "SERVICE_UNAVAILABLE");
        fallbackData.put("fallback", true);

        return AjaxResult.error("气象预警服务不可用").put("data", fallbackData);
    }
}
