package com.tocc.feign.weather;

import feign.Request;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 气象预警 Feign 客户端专用配置
 * 
 * <AUTHOR>
 */
@Configuration
public class WeatherWarningFeignConfig {

    /**
     * 气象预警服务专用超时配置
     * 考虑到预警查询可能涉及复杂的权限计算，适当延长超时时间
     */
    @Bean
    public Request.Options weatherWarningOptions() {
        // 连接超时5秒，读取超时15秒
        return new Request.Options(5000, 15000);
    }
}
