package com.tocc.feign.weather;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.Map;

/**
 * 气象预警 Feign 客户端
 *
 * <AUTHOR>
 */
@FeignClient(
    name = "weather-service",
    url = "${feign.weather.url:http://localhost:8080}",
    fallback = WeatherWarningFeignClientFallback.class,
    configuration = WeatherWarningFeignConfig.class
)
public interface WeatherWarningFeignClient {

    /**
     * 根据预警ID获取预警详情
     *
     * @param warningId 预警ID
     * @return 预警详情
     */
    @GetMapping("/weather/warning/{warningId}")
    Map<String, Object> getWarningDetail(@PathVariable("warningId") String warningId);
}
