package com.tocc.feign.alarm;

import com.tocc.common.core.domain.AjaxResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

/**
 * 告警服务 Feign 客户端
 * 
 * <AUTHOR>
 */
@FeignClient(
    name = "alarm-service",
    url = "${feign.alarm.url:http://localhost:8080}",
    fallback = AlarmFeignClientFallback.class
)
public interface AlarmFeignClient {

    /**
     * 创建告警
     * 
     * @param alarmInfo 告警信息
     * @return 创建结果
     */
    @PostMapping("/alarm/info/create")
    AjaxResult createAlarm(@RequestBody Map<String, Object> alarmInfo);
}
