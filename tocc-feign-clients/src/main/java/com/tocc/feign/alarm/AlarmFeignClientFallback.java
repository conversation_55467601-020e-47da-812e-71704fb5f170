package com.tocc.feign.alarm;

import com.tocc.common.core.domain.AjaxResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 告警服务 Feign 客户端降级处理
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class AlarmFeignClientFallback implements AlarmFeignClient {

    @Override
    public AjaxResult createAlarm(Map<String, Object> alarmInfo) {
        log.warn("告警服务调用失败，执行降级处理: alarmInfo={}", alarmInfo);
        
        // 降级处理：记录日志，返回失败结果
        return AjaxResult.error("告警服务暂时不可用，告警创建失败");
    }
}
