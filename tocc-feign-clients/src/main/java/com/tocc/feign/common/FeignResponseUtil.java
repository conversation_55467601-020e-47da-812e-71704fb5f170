package com.tocc.feign.common;

import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * Feign 响应处理工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class FeignResponseUtil {

    /**
     * 处理 Feign 调用响应
     *
     * @param response Feign 调用响应
     * @param sourceId 数据源ID
     * @param sourceType 数据源类型
     * @return 处理后的数据
     */
    public static Map<String, Object> handleResponse(Map<String, Object> response, String sourceId, String sourceType) {
        if (response == null) {
            return createErrorDetail(sourceId, "调用" + sourceType + "服务返回空响应");
        }

        Integer code = (Integer) response.get("code");
        if (code != null && code == 200) {
            // 成功响应，返回数据
            Object data = response.get("data");
            if (data instanceof Map) {
                return (Map<String, Object>) data;
            } else {
                log.warn("{}服务返回数据格式异常: sourceId={}, data={}", sourceType, sourceId, data);
                return createErrorDetail(sourceId, sourceType + "服务返回数据格式异常");
            }
        } else {
            // 错误响应
            String message = (String) response.get("msg");
            if (message == null) {
                message = "调用" + sourceType + "服务失败";
            }
            return createErrorDetail(sourceId, message);
        }
    }

    /**
     * 创建错误详情
     * 
     * @param sourceId 数据源ID
     * @param message 错误信息
     * @return 错误详情
     */
    public static Map<String, Object> createErrorDetail(String sourceId, String message) {
        Map<String, Object> detail = new HashMap<>();
        detail.put("sourceId", sourceId);
        detail.put("message", message);
        detail.put("status", "ERROR");
        detail.put("timestamp", System.currentTimeMillis());
        return detail;
    }

    /**
     * 创建服务不可用详情
     * 
     * @param sourceId 数据源ID
     * @param serviceType 服务类型
     * @return 服务不可用详情
     */
    public static Map<String, Object> createServiceUnavailableDetail(String sourceId, String serviceType) {
        Map<String, Object> detail = new HashMap<>();
        detail.put("sourceId", sourceId);
        detail.put("message", serviceType + "服务暂时不可用，请稍后重试");
        detail.put("status", "SERVICE_UNAVAILABLE");
        detail.put("serviceType", serviceType);
        detail.put("timestamp", System.currentTimeMillis());
        return detail;
    }
}
