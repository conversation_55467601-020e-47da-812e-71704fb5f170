package com.tocc.feign.system;

import com.tocc.common.core.domain.AjaxResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * 系统字典数据 Feign 客户端
 * 
 * <AUTHOR>
 */
@FeignClient(
    name = "system-dict-service",
    url = "${feign.system.url:http://localhost:8080}",
    fallback = SysDictDataFeignClientFallback.class
)
public interface SysDictDataFeignClient {

    /**
     * 根据字典类型和字典键值查询字典数据信息
     * 
     * @param dictType 字典类型
     * @param dictValue 字典键值
     * @return 字典标签
     */
    @GetMapping("/system/dict/data/label/{dictType}/{dictValue}")
    AjaxResult selectDictLabel(@PathVariable("dictType") String dictType, @PathVariable("dictValue") String dictValue);
}
