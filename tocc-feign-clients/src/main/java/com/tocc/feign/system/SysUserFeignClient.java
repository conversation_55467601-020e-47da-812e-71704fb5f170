package com.tocc.feign.system;

import com.tocc.common.core.domain.AjaxResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * 系统用户 Feign 客户端
 * 
 * <AUTHOR>
 */
@FeignClient(
    name = "system-user-service",
    url = "${feign.system.url:http://localhost:8080}",
    fallback = SysUserFeignClientFallback.class
)
public interface SysUserFeignClient {

    /**
     * 通过用户ID查询用户
     * 
     * @param userId 用户ID
     * @return 用户对象信息
     */
    @GetMapping("/system/user/{userId}")
    AjaxResult selectUserById(@PathVariable("userId") Long userId);
}
