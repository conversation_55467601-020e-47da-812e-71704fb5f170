package com.tocc.feign.system;

import com.tocc.common.core.domain.AjaxResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 系统用户 Feign 客户端降级处理
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class SysUserFeignClientFallback implements SysUserFeignClient {

    @Override
    public AjaxResult selectUserById(Long userId) {
        log.warn("系统用户服务调用失败，执行降级处理: userId={}", userId);
        
        // 降级处理：返回空用户信息
        return AjaxResult.error("用户服务不可用");
    }
}
