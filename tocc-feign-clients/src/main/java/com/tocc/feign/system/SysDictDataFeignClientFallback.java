package com.tocc.feign.system;

import com.tocc.common.core.domain.AjaxResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 系统字典数据 Feign 客户端降级处理
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class SysDictDataFeignClientFallback implements SysDictDataFeignClient {

    @Override
    public AjaxResult selectDictLabel(String dictType, String dictValue) {
        log.warn("系统字典服务调用失败，执行降级处理: dictType={}, dictValue={}", dictType, dictValue);
        
        // 降级处理：返回字典值本身作为标签
        return AjaxResult.success(dictValue);
    }
}
