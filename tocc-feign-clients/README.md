# Feign 客户端模块

## 概述

`tocc-feign-clients` 模块是专门用于管理各模块间远程调用的 Feign 客户端模块。通过统一的客户端管理，实现模块间的解耦和服务治理。

## 模块结构

```
tocc-feign-clients/
├── src/main/java/com/tocc/feign/
│   ├── config/                 # 全局配置
│   │   ├── FeignConfig.java
│   │   └── FeignErrorDecoder.java
│   ├── weather/                # 气象预警客户端
│   │   ├── WeatherWarningFeignClient.java
│   │   ├── WeatherWarningFeignConfig.java
│   │   └── WeatherWarningFeignClientFallback.java
│   ├── emergency/              # 应急事件客户端（待扩展）
│   ├── system/                 # 系统服务客户端（待扩展）
│   └── common/                 # 公共工具
│       └── FeignResponseUtil.java
└── src/main/resources/
    └── application.yml         # 配置文件
```

## 功能特性

### 1. 统一配置管理
- 全局超时配置
- 统一错误处理
- 请求拦截器（认证信息传递）
- 日志配置

### 2. 服务降级
- 自动降级处理
- 友好的错误响应
- 服务不可用时的备选方案

### 3. 负载均衡
- 支持多实例调用
- 自动故障转移

### 4. 监控和日志
- 详细的调用日志
- 性能监控
- 错误统计

## 使用方法

### 1. 添加依赖

在需要使用 Feign 客户端的模块中添加依赖：

```xml
<dependency>
    <groupId>com.tocc</groupId>
    <artifactId>tocc-feign-clients</artifactId>
</dependency>
```

### 2. 启用 Feign 客户端

在启动类上添加注解：

```java
@SpringBootApplication
@EnableFeignClients(basePackages = "com.tocc.feign")
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
```

### 3. 注入和使用

```java
@Service
public class AlarmServiceImpl implements IAlarmService {
    
    @Autowired
    private WeatherWarningFeignClient weatherWarningFeignClient;
    
    public Map<String, Object> getWeatherWarningDetail(String warningId) {
        try {
            AjaxResult response = weatherWarningFeignClient.getWarningDetail(warningId);
            return FeignResponseUtil.handleResponse(response, warningId, "气象预警");
        } catch (Exception e) {
            return FeignResponseUtil.createErrorDetail(warningId, "调用气象预警服务异常：" + e.getMessage());
        }
    }
}
```

## 配置说明

### 服务地址配置

```yaml
feign:
  weather:
    url: http://localhost:8080
  emergency:
    url: http://localhost:8080
```

### 超时配置

```yaml
feign:
  client:
    config:
      default:
        connectTimeout: 5000
        readTimeout: 10000
      weather-service:
        connectTimeout: 5000
        readTimeout: 15000
```

## 扩展指南

### 添加新的服务客户端

1. 在对应的包下创建 Feign 客户端接口
2. 创建降级处理类
3. 添加专用配置（如需要）
4. 更新配置文件

### 示例：添加应急事件客户端

```java
@FeignClient(
    name = "emergency-service",
    url = "${feign.emergency.url}",
    fallback = EmergencyEventFeignClientFallback.class
)
public interface EmergencyEventFeignClient {
    
    @GetMapping("/emergency/event/{eventId}")
    AjaxResult getEventDetail(@PathVariable("eventId") String eventId);
}
```

## 注意事项

1. **认证传递**：自动传递 Authorization 头信息
2. **错误处理**：统一的错误处理和降级机制
3. **超时配置**：根据不同服务特点配置合适的超时时间
4. **日志级别**：生产环境建议使用 BASIC 级别
5. **服务地址**：支持通过环境变量配置服务地址

## 版本历史

- v1.0.0: 初始版本，支持气象预警服务调用
- 待扩展: 应急事件、系统服务等其他模块的客户端
