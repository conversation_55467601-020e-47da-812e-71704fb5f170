package com.tocc.weather.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.tocc.system.service.ISysDictDataService;
import com.tocc.weather.utils.WeatherWarningUtils;

/**
 * 气象预警配置类
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Component
public class WeatherWarningConfig
{
    @Autowired
    private ISysDictDataService dictDataService;

    /**
     * 根据预警等级获取超时时间
     * 
     * @param warningLevel 预警等级
     * @return 超时时间（分钟）
     */
    public int getTimeoutMinutes(String warningLevel) 
    {
        return WeatherWarningUtils.getTimeoutMinutesByLevel(warningLevel);
    }

    /**
     * 获取预警等级显示名称
     * 
     * @param warningLevel 预警等级
     * @return 等级名称
     */
    public String getWarningLevelLabel(String warningLevel)
    {
        String label = dictDataService.selectDictLabel("alarm_level", warningLevel);
        return label != null ? label : WeatherWarningUtils.getWarningLevelName(warningLevel);
    }

    /**
     * 获取预警类型显示名称
     * 
     * @param warningType 预警类型
     * @return 类型名称
     */
    public String getWarningTypeLabel(String warningType)
    {
        String label = dictDataService.selectDictLabel("weather_warning_type", warningType);
        return label != null ? label : WeatherWarningUtils.getWarningTypeName(warningType);
    }

    /**
     * 获取状态显示名称
     * 
     * @param status 状态
     * @return 状态名称
     */
    public String getStatusLabel(String status) 
    {
        return WeatherWarningUtils.getStatusName(status);
    }

    /**
     * 验证预警等级是否有效
     * 
     * @param warningLevel 预警等级
     * @return 是否有效
     */
    public boolean isValidWarningLevel(String warningLevel) 
    {
        return WeatherWarningUtils.isValidWarningLevel(warningLevel);
    }

    /**
     * 验证预警类型是否有效
     * 
     * @param warningType 预警类型
     * @return 是否有效
     */
    public boolean isValidWarningType(String warningType) 
    {
        return WeatherWarningUtils.isValidWarningType(warningType);
    }

    /**
     * 验证预警状态是否有效
     * 
     * @param status 状态
     * @return 是否有效
     */
    public boolean isValidStatus(String status) 
    {
        return WeatherWarningUtils.isValidStatus(status);
    }

    /**
     * 比较预警等级优先级
     * 
     * @param level1 等级1
     * @param level2 等级2
     * @return 比较结果
     */
    public int compareWarningLevel(String level1, String level2) 
    {
        return WeatherWarningUtils.compareWarningLevel(level1, level2);
    }

    /**
     * 获取默认超时时间
     * 
     * @return 默认超时时间（分钟）
     */
    public int getDefaultTimeoutMinutes() 
    {
        return 30;
    }

    /**
     * 获取预警即将过期提醒时间
     * 
     * @return 提前提醒时间（分钟）
     */
    public int getExpiringReminderMinutes() 
    {
        return 30;
    }

    /**
     * 获取历史数据保留天数
     * 
     * @return 保留天数
     */
    public int getHistoryRetentionDays() 
    {
        return 180; // 默认保留6个月
    }

    /**
     * 是否启用短信通知
     * 
     * @return 是否启用
     */
    public boolean isSmsNotificationEnabled() 
    {
        // TODO: 从配置文件或数据库读取
        return true;
    }

    /**
     * 是否启用邮件通知
     * 
     * @return 是否启用
     */
    public boolean isEmailNotificationEnabled() 
    {
        // TODO: 从配置文件或数据库读取
        return true;
    }

    /**
     * 是否启用系统通知
     * 
     * @return 是否启用
     */
    public boolean isSystemNotificationEnabled() 
    {
        // TODO: 从配置文件或数据库读取
        return true;
    }

    /**
     * 获取最大重试次数
     * 
     * @return 重试次数
     */
    public int getMaxRetryCount() 
    {
        return 3;
    }

    /**
     * 获取重试间隔时间
     * 
     * @return 间隔时间（秒）
     */
    public int getRetryIntervalSeconds() 
    {
        return 60;
    }
}
