package com.tocc.weather.service;

import java.util.List;
import java.util.Map;

import com.tocc.weather.domain.entity.WeatherWarning;
import com.tocc.weather.domain.dto.WeatherWarningDTO;
import com.tocc.weather.domain.dto.WeatherWarningCreateDTO;
import com.tocc.weather.domain.vo.WeatherWarningVO;


/**
 * 气象预警信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface IWeatherWarningService 
{
    /**
     * 查询气象预警信息
     * 
     * @param warningId 气象预警信息主键
     * @return 气象预警信息
     */
    public WeatherWarning selectWeatherWarningByWarningId(String warningId);

    /**
     * 查询气象预警信息列表
     * 
     * @param weatherWarningDTO 气象预警信息查询条件
     * @return 气象预警信息集合
     */
    public List<WeatherWarning> selectWeatherWarningList(WeatherWarningDTO weatherWarningDTO);

    /**
     * 修改气象预警信息
     * 
     * @param weatherWarning 气象预警信息
     * @return 结果
     */
    public int updateWeatherWarning(WeatherWarning weatherWarning);

    /**
     * 批量删除气象预警信息
     * 
     * @param warningIds 需要删除的气象预警信息主键集合
     * @return 结果
     */
    public int deleteWeatherWarningByWarningIds(String[] warningIds);


    /**
     * 创建预警
     *
     * @param createDTO 创建预警DTO
     * @return 预警ID
     */
    public String createWarning(WeatherWarningCreateDTO createDTO);

    /**
     * 根据用户ID列表发送预警通知
     *
     * @param warningId 预警ID
     * @param userIds 用户ID列表
     * @return 发送成功的通知数量
     */
    public int sendNotificationsByUserIds(String warningId, List<Long> userIds);

    /**
     * 获取预警详情（包含统计信息）
     * 
     * @param warningId 预警ID
     * @return 预警详情VO
     */
    public WeatherWarningVO selectWeatherWarningDetail(String warningId);

    /**
     * 更新预警状态
     *
     * @param warningId 预警ID
     * @param status 状态
     * @return 结果
     */
    public int updateWeatherWarningStatus(String warningId, String status);

    /**
     * 统计未失效的预警数量（按等级分组）
     * 基于用户权限控制，只统计用户有权限查看的预警
     *
     * @return 预警等级统计结果
     */
    public Map<String, Integer> selectWarningLevelStatistics();

    /**
     * 根据预警ID查询预警详情（基于权限控制）
     * 参考 detail-list 接口的权限控制逻辑和返回数据结构
     *
     * @param warningId 预警ID
     * @return 预警详情VO，如果无权限或不存在则返回null
     */
    public WeatherWarningVO selectWeatherWarningById(String warningId);

    /**
     * 根据预警ID查询预警详情（基于权限控制）
     * 参考 detail-list 接口的权限控制逻辑
     *
     * @param warningId 预警ID
     * @return 预警详情VO，如果无权限或不存在则返回null
     */
    public WeatherWarningVO selectWeatherWarningById(String warningId);

    /**
     * 查询气象预警详细列表（包含影响区域和通知记录）
     *
     * @param queryDTO 查询条件
     * @return 预警详细列表
     */
    public List<WeatherWarningVO> selectWeatherWarningDetailList(WeatherWarningDTO queryDTO);

}
