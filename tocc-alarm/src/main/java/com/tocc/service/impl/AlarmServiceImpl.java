package com.tocc.service.impl;

import com.tocc.common.annotation.DataScope;
import com.tocc.common.utils.DateUtils;
import com.tocc.common.utils.SecurityUtils;
import com.tocc.common.utils.uuid.IdUtils;
import com.tocc.domain.dto.AlarmInfoDTO;
import com.tocc.domain.vo.AlarmInfoVO;
import com.tocc.mapper.AlarmInfoMapper;
import com.tocc.service.IAlarmService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 告警信息Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class AlarmServiceImpl implements IAlarmService {

    @Autowired
    private AlarmInfoMapper alarmInfoMapper;

    /**
     * 查询告警信息
     * 
     * @param alarmId 告警信息主键
     * @return 告警信息
     */
    @Override
    public AlarmInfoVO selectAlarmInfoByAlarmId(String alarmId) {
        return alarmInfoMapper.selectAlarmInfoByAlarmId(alarmId);
    }

    /**
     * 查询告警信息列表
     *
     * @param alarmInfo 告警信息
     * @return 告警信息
     */
    @Override
    @DataScope(deptAlias = "a")
    public List<AlarmInfoVO> selectAlarmInfoList(AlarmInfoDTO alarmInfo) {
        return alarmInfoMapper.selectAlarmInfoList(alarmInfo);
    }

    /**
     * 新增告警信息
     * 
     * @param alarmInfo 告警信息
     * @return 结果
     */
    @Override
    public int insertAlarmInfo(AlarmInfoDTO alarmInfo) {
        // 设置默认值
        if (alarmInfo.getAlarmId() == null || alarmInfo.getAlarmId().trim().isEmpty()) {
            alarmInfo.setAlarmId(IdUtils.fastSimpleUUID());
        }
        if (alarmInfo.getAlarmTime() == null) {
            alarmInfo.setAlarmTime(new Date());
        }
        if (alarmInfo.getStatus() == null || alarmInfo.getStatus().trim().isEmpty()) {
            alarmInfo.setStatus("0"); // 默认未处理
        }
        
        alarmInfo.setCreateBy(SecurityUtils.getUsername());
        alarmInfo.setCreateTime(DateUtils.getNowDate());
        
        return alarmInfoMapper.insertAlarmInfo(alarmInfo);
    }

    /**
     * 修改告警信息
     * 
     * @param alarmInfo 告警信息
     * @return 结果
     */
    @Override
    public int updateAlarmInfo(AlarmInfoDTO alarmInfo) {
        alarmInfo.setUpdateBy(SecurityUtils.getUsername());
        alarmInfo.setUpdateTime(DateUtils.getNowDate());
        return alarmInfoMapper.updateAlarmInfo(alarmInfo);
    }

    /**
     * 批量删除告警信息
     * 
     * @param alarmIds 需要删除的告警信息主键
     * @return 结果
     */
    @Override
    public int deleteAlarmInfoByAlarmIds(String[] alarmIds) {
        return alarmInfoMapper.deleteAlarmInfoByAlarmIds(alarmIds);
    }

    /**
     * 删除告警信息信息
     * 
     * @param alarmId 告警信息主键
     * @return 结果
     */
    @Override
    public int deleteAlarmInfoByAlarmId(String alarmId) {
        return alarmInfoMapper.deleteAlarmInfoByAlarmId(alarmId);
    }

    /**
     * 处理告警
     * 
     * @param alarmId 告警ID
     * @param status 处理状态（1已处理 2已忽略）
     * @param processResult 处理结果
     * @return 结果
     */
    @Override
    public int processAlarm(String alarmId, String status, String processResult) {
        String processorId = SecurityUtils.getUserId().toString();
        String processorName = SecurityUtils.getUsername();
        String updateBy = SecurityUtils.getUsername();
        
        return alarmInfoMapper.updateAlarmStatus(alarmId, status, processorId, 
                                               processorName, processResult, updateBy);
    }

    /**
     * 统计告警数量
     * 
     * @param alarmInfo 查询条件
     * @return 统计数量
     */
    @Override
    public int countAlarmInfo(AlarmInfoDTO alarmInfo) {
        return alarmInfoMapper.countAlarmInfo(alarmInfo);
    }



    /**
     * 检查是否存在相同的超时告警
     *
     * @param infoType 信息类型
     * @param infoId 信息ID
     * @return 是否存在
     */
    @Override
    public boolean existsTimeoutAlarm(String infoType, String infoId) {
        // 查询最近7天内是否已有相同的超时告警
        LocalDateTime sevenDaysAgo = LocalDateTime.now().minusDays(7);

        return alarmInfoMapper.existsTimeoutAlarm(infoType, infoId, sevenDaysAgo);
    }

    /**
     * 创建告警
     *
     * @param alarmInfo 告警信息
     * @return 结果
     */
    @Override
    public int createAlarm(AlarmInfoDTO alarmInfo) {
        return insertAlarmInfo(alarmInfo);
    }

    /**
     * 获取告警数据源详情
     * 注意：此方法已废弃，逻辑已移至 Controller 层处理
     *
     * @param alarmId 告警ID
     * @return 数据源详情
     */
    @Override
    @Deprecated
    public Map<String, Object> getAlarmSourceDetail(String alarmId) {
        // 此方法已废弃，逻辑已移至 AlarmController 层处理
        // 保留此方法是为了避免接口变更
        Map<String, Object> result = new HashMap<>();
        result.put("message", "此方法已废弃，请使用 Controller 层的实现");
        result.put("status", "DEPRECATED");
        return result;
    }

    /**
     * 获取应急事件详情
     *
     * @param eventId 事件ID
     * @return 事件详情
     */
    private Map<String, Object> getEmergencyEventDetail(String eventId) {
        // TODO: 调用应急事件模块的详情接口
        // 这里需要注入应急事件的Service，然后调用其详情查询方法
        Map<String, Object> detail = new HashMap<>();
        detail.put("eventId", eventId);
        detail.put("message", "应急事件详情接口待实现");
        detail.put("todo", "请应急事件模块负责人实现此方法");
        return detail;
    }

    /**
     * 获取气象预警详情
     *
     * @param warningId 预警ID
     * @return 预警详情
     */
    private Map<String, Object> getWeatherWarningDetail(String warningId) {
        // TODO: 调用气象预警模块的详情接口
        // 由于模块间依赖问题，暂时返回TODO信息
        // 后续可以通过以下方式实现：
        // 1. 配置模块依赖关系
        // 2. 使用 Feign 客户端进行远程调用
        // 3. 使用消息队列进行异步通信
        // 4. 使用 Spring Cloud Gateway 进行接口代理

        Map<String, Object> detail = new HashMap<>();
        detail.put("warningId", warningId);
        detail.put("message", "气象预警详情接口已实现，但需要配置模块间依赖");
        detail.put("todo", "请配置模块依赖或使用其他方式调用气象预警详情接口");
        detail.put("availableInterface", "GET /weather/warning/" + warningId);
        detail.put("status", "TODO");
        return detail;
    }

    /**
     * 创建TODO详情对象
     *
     * @param message 提示信息
     * @return TODO详情
     */
    private Map<String, Object> createTodoDetail(String message) {
        Map<String, Object> detail = new HashMap<>();
        detail.put("message", message);
        detail.put("status", "TODO");
        return detail;
    }
}
